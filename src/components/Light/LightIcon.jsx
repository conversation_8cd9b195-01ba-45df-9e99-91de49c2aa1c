const LightIcon = ({currentColor, currentEffect}) => {
  return (
    <>
      {currentEffect === 0 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M24 4C12.96 4 4 12.96 4 24C4 35.04 12.96 44 24 44C35.04 44 44 35.04 44 24C44 12.96 35.04 4 24 4ZM8 24C8 15.16 15.16 8 24 8C27.7 8 31.1 9.26 33.8 11.38L11.38 33.8C9.26 31.1 8 27.7 8 24ZM24 40C20.3 40 16.9 38.74 14.2 36.62L36.62 14.2C38.74 16.9 40 20.3 40 24C40 32.84 32.84 40 24 40Z" fill={currentColor} fillOpacity="0.85"/>
      </svg>}
      {currentEffect === 1 && <svg width="36" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M32.5002 10.3495L38.1602 16.0095L32.5002 21.6695L26.8402 16.0095L32.5002 10.3495ZM17.1802 11.3095V19.3095H9.18018V11.3095H17.1802ZM37.1802 31.3094V39.3094H29.1802V31.3094H37.1802ZM17.1802 31.3094V39.3094H9.18018V31.3094H17.1802ZM32.5002 4.68945L21.1802 15.9895L32.5002 27.3094L43.8202 15.9895L32.5002 4.68945ZM21.1802 7.30945H5.18018V23.3094H21.1802V7.30945ZM41.1802 27.3094H25.1802V43.3094H41.1802V27.3094ZM21.1802 27.3094H5.18018V43.3094H21.1802V27.3094Z" fill={currentColor} fillOpacity="0.85"/>
      </svg>}
      {currentEffect === 45 && <svg width="36" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M20.5 40C9.48 40 0.5 31.02 0.5 20C0.5 8.98 9.48 0 20.5 0C31.52 0 40.5 8.08 40.5 18C40.5 24.62 35.12 30 28.5 30H24.96C24.4 30 23.96 30.44 23.96 31C23.96 31.24 24.06 31.46 24.22 31.66C25.04 32.6 25.5 33.78 25.5 35C25.5 37.76 23.26 40 20.5 40ZM20.5 4C11.68 4 4.5 11.18 4.5 20C4.5 28.82 11.68 36 20.5 36C21.06 36 21.5 35.56 21.5 35C21.5 34.68 21.34 34.44 21.22 34.3C20.4 33.38 19.96 32.2 19.96 31C19.96 28.24 22.2 26 24.96 26H28.5C32.92 26 36.5 22.42 36.5 18C36.5 10.28 29.32 4 20.5 4Z" fill={currentColor} fillOpacity="0.85"/>
        <path d="M9.5 22C11.1569 22 12.5 20.6569 12.5 19C12.5 17.3431 11.1569 16 9.5 16C7.84315 16 6.5 17.3431 6.5 19C6.5 20.6569 7.84315 22 9.5 22Z" fill={currentColor} fillOpacity="0.85"/>
        <path d="M15.5 14C17.1569 14 18.5 12.6569 18.5 11C18.5 9.34315 17.1569 8 15.5 8C13.8431 8 12.5 9.34315 12.5 11C12.5 12.6569 13.8431 14 15.5 14Z" fill={currentColor} fillOpacity="0.85"/>
        <path d="M25.5 14C27.1569 14 28.5 12.6569 28.5 11C28.5 9.34315 27.1569 8 25.5 8C23.8431 8 22.5 9.34315 22.5 11C22.5 12.6569 23.8431 14 25.5 14Z" fill={currentColor} fillOpacity="0.85"/>
        <path d="M31.5 22C33.1569 22 34.5 20.6569 34.5 19C34.5 17.3431 33.1569 16 31.5 16C29.8431 16 28.5 17.3431 28.5 19C28.5 20.6569 29.8431 22 31.5 22Z" fill={currentColor} fillOpacity="0.85"/>
      </svg>}
      {currentEffect === 2 && <svg width="36" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M18.5 21L22.5 21L22.5 27L18.5 27L18.5 32L14.5 32L14.5 27L2.5 27L2.5 41L14.5 41L14.5 36L18.5 36L18.5 41L30.5 41L30.5 36L34.5 36L34.5 41L46.5 41L46.5 27L34.5 27L34.5 32L30.5 32L30.5 27L26.5 27L26.5 21L30.5 21L30.5 7L18.5 7L18.5 21ZM6.5 37L6.5 31L10.5 31L10.5 37L6.5 37ZM42.5 31L42.5 37L38.5 37L38.5 31L42.5 31ZM26.5 31L26.5 37L22.5 37L22.5 31L26.5 31ZM26.5 11L26.5 17L22.5 17L22.5 11L26.5 11Z" fill={currentColor} fillOpacity="0.85"/>
      </svg>}
      {currentEffect === 3 && <svg width="36" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M22.5 18H26.5V22H22.5V18ZM18.5 22H22.5V26H18.5V22ZM26.5 22H30.5V26H26.5V22ZM30.5 18H34.5V22H30.5V18ZM14.5 18H18.5V22H14.5V18ZM38.5 6H10.5C8.3 6 6.5 7.8 6.5 10V38C6.5 40.2 8.3 42 10.5 42H38.5C40.7 42 42.5 40.2 42.5 38V10C42.5 7.8 40.7 6 38.5 6ZM18.5 36H14.5V32H18.5V36ZM26.5 36H22.5V32H26.5V36ZM34.5 36H30.5V32H34.5V36ZM38.5 22H34.5V26H38.5V30H34.5V26H30.5V30H26.5V26H22.5V30H18.5V26H14.5V30H10.5V26H14.5V22H10.5V10H38.5V22Z" fill={currentColor} fillOpacity="0.85"/>
      </svg>}
      {currentEffect === 4 && <svg width="36" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M30.5 22.002L30.5 26.002L26.5 26.002L26.5 22.002L30.5 22.002ZM26.5 18.002L26.5 22.002L22.5 22.002L22.5 18.002L26.5 18.002ZM26.5 26.002L26.5 30.002L22.5 30.002L22.5 26.002L26.5 26.002ZM30.5 30.002L30.5 34.002L26.5 34.002L26.5 30.002L30.5 30.002ZM30.5 14.002L30.5 18.002L26.5 18.002L26.5 14.002L30.5 14.002ZM42.5 38.002L42.5 10.002C42.5 7.80195 40.7 6.00195 38.5 6.00195L10.5 6.00195C8.3 6.00195 6.5 7.80195 6.5 10.002L6.5 38.002C6.5 40.202 8.3 42.002 10.5 42.002L38.5 42.002C40.7 42.002 42.5 40.202 42.5 38.002ZM12.5 18.002L12.5 14.002L16.5 14.002L16.5 18.002L12.5 18.002ZM12.5 26.002L12.5 22.002L16.5 22.002L16.5 26.002L12.5 26.002ZM12.5 34.002L12.5 30.002L16.5 30.002L16.5 34.002L12.5 34.002ZM26.5 38.002L26.5 34.002L22.5 34.002L22.5 38.002L18.5 38.002L18.5 34.002L22.5 34.002L22.5 30.002L18.5 30.002L18.5 26.002L22.5 26.002L22.5 22.002L18.5 22.002L18.5 18.002L22.5 18.002L22.5 14.002L18.5 14.002L18.5 10.002L22.5 10.002L22.5 14.002L26.5 14.002L26.5 10.002L38.5 10.002L38.5 38.002L26.5 38.002Z" fill={currentColor} fillOpacity="0.85"/>
      </svg>}
      {currentEffect === 5 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 22H4C2.9 22 2 22.9 2 24C2 25.1 2.9 26 4 26H12C13.1 26 14 25.1 14 24C14 22.9 13.1 22 12 22ZM16.94 14.12L15.5 12.68C14.72 11.9 13.46 11.9 12.68 12.68C11.9 13.46 11.9 14.72 12.68 15.5L14.1 16.92C14.88 17.7 16.14 17.7 16.92 16.92C17.7 16.16 17.7 14.88 16.94 14.12ZM24 2C22.88 2 22 2.9 22 4V12C22 13.1 22.9 14 24 14C25.1 14 26 13.1 26 12V4C26 2.9 25.1 2 24 2ZM35.32 12.7C34.54 11.92 33.28 11.92 32.5 12.7L31.08 14.12C30.3 14.9 30.3 16.16 31.08 16.94C31.86 17.72 33.12 17.72 33.9 16.94L35.32 15.52C36.08 14.74 36.08 13.46 35.32 12.7ZM34 24C34 25.12 34.9 26 36 26H44C45.1 26 46 25.1 46 24C46 22.9 45.1 22 44 22H36C34.9 22 34 22.9 34 24ZM24 18C20.68 18 18 20.68 18 24C18 27.32 20.68 30 24 30C27.32 30 30 27.32 30 24C30 20.68 27.32 18 24 18ZM31.06 33.88L32.48 35.3C33.26 36.08 34.52 36.08 35.3 35.3C36.08 34.52 36.08 33.26 35.3 32.48L33.88 31.06C33.1 30.28 31.84 30.28 31.06 31.06C30.3 31.84 30.3 33.12 31.06 33.88ZM12.68 35.3C13.46 36.08 14.72 36.08 15.5 35.3L16.92 33.88C17.7 33.1 17.7 31.84 16.92 31.06C16.14 30.28 14.88 30.28 14.1 31.06L12.68 32.48C11.92 33.26 11.92 34.54 12.68 35.3ZM24 46C25.12 46 26 45.1 26 44V36C26 34.9 25.1 34 24 34C22.9 34 22 34.9 22 36V44C22 45.1 22.9 46 24 46Z" fill={currentColor} fillOpacity="0.85"/>
      </svg>}
      {currentEffect === 6 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M37.36 34.32C37.7 34.1 38.12 34 38.64 34C39.18 34 39.64 34.12 40.04 34.38C40.42 34.64 40.76 35.06 41.04 35.64L43.6 34.44C42.6 32.34 40.98 31.28 38.68 31.28C37.78 31.28 36.96 31.46 36.22 31.8C35.48 32.14 34.9 32.62 34.46 33.22C34.02 33.82 33.82 34.5 33.82 35.28C33.82 36.14 34.04 36.84 34.48 37.36C34.92 37.9 35.42 38.28 36 38.52C36.58 38.76 37.28 39.02 38.16 39.26C38.82 39.44 39.3 39.6 39.64 39.74C39.98 39.88 40.2 40.04 40.32 40.2C40.44 40.36 40.52 40.56 40.52 40.8C40.52 41.16 40.34 41.44 39.98 41.66C39.62 41.88 39.14 41.98 38.54 41.98C37.94 41.98 37.44 41.84 36.98 41.54C36.52 41.24 36.2 40.8 35.98 40.22L33.4 41.42C33.82 42.4 34.48 43.18 35.38 43.78C36.28 44.38 37.32 44.68 38.52 44.68C39.5 44.68 40.38 44.52 41.14 44.18C41.9 43.84 42.5 43.38 42.92 42.76C43.34 42.14 43.56 41.44 43.56 40.62C43.56 39.74 43.34 39.04 42.9 38.52C42.46 38 41.96 37.62 41.38 37.36C40.8 37.12 40.1 36.88 39.26 36.66C38.6 36.48 38.1 36.32 37.78 36.18C37.46 36.04 37.22 35.88 37.1 35.72C36.96 35.56 36.9 35.34 36.9 35.08C36.9 34.76 37.06 34.5 37.42 34.28L37.36 34.32Z" fill={currentColor} fillOpacity="0.85"/>
      <path d="M24 21C16.28 21 10 27.28 10 35H14C14 29.48 18.48 25 24 25C27.56 25 30.68 26.86 32.46 29.66C33.58 28.92 34.86 28.4 36.22 28.16C33.82 23.9 29.24 21 24 21Z" fill={currentColor} fillOpacity="0.85"/>
      <path d="M24 13C11.86 13 2 22.86 2 35H6C6 25.08 14.08 17 24 17C31.58 17 38.1 21.72 40.72 28.38C42.72 28.94 44.46 30.12 45.74 31.68C44.16 21.12 35.02 13 24 13Z" fill={currentColor} fillOpacity="0.85"/>
      </svg>}
      {currentEffect === 7 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M24 21C16.28 21 10 27.28 10 35H14C14 29.48 18.48 25 24 25C27.56 25 30.68 26.86 32.46 29.66C33.58 28.92 34.86 28.4 36.22 28.16C33.82 23.9 29.24 21 24 21Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M24 13C11.86 13 2 22.86 2 35H6C6 25.08 14.08 17 24 17C31.58 17 38.1 21.72 40.72 28.38C42.72 28.94 44.46 30.12 45.74 31.68C44.16 21.12 35.02 13 24 13Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M36.4 45.3L31.58 32.34H34.78L37.86 41.26H37.96L41.04 32.34H44.24L39.42 45.3H36.38H36.4Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
      {currentEffect === 8 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M44 22H32.92C34.82 19.88 36 17.08 36 14C36 7.38 30.62 2 24 2C22.88 2 22 2.9 22 4V15.08C19.86 13.16 17.08 12 14 12C7.36 12 2 17.38 2 24C2 25.1 2.88 26 4 26H15.06C13.16 28.12 12 30.92 12 34C12 40.62 17.36 46 24 46C25.1 46 26 45.1 26 44V32.92C26.78 33.62 27.62 34.22 28.56 34.68C29.02 33.4 29.72 32.24 30.62 31.26C28.48 30.26 26.84 28.34 26.24 26H41.74C41.52 26.84 41.18 27.64 40.7 28.36C42.02 28.74 43.22 29.36 44.26 30.2C45.38 28.4 46 26.28 46 24C46 22.9 45.1 22 44 22ZM6.24 22C7.14 18.54 10.26 16 14 16C17.74 16 20.86 18.54 21.74 22H6.24ZM22 41.74C18.54 40.86 16 37.72 16 34C16 30.28 18.54 27.14 22 26.26V41.74ZM26 21.74V6.26C29.44 7.14 32 10.28 32 14C32 17.72 29.44 20.86 26 21.74Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M36.88 34.3208C37.22 34.1008 37.64 34.0008 38.16 34.0008C38.68 34.0008 39.18 34.1208 39.56 34.3808C39.62 34.4208 39.7 34.4808 39.76 34.5408C40.06 34.8008 40.32 35.1608 40.54 35.6408L43.1 34.4408C42.82 33.8208 42.48 33.3008 42.06 32.8808C41.1 31.8208 39.82 31.3008 38.2 31.3008C37.78 31.3008 37.36 31.3408 36.98 31.4208C36.58 31.5808 36.18 31.7208 35.74 31.8008C35 32.1608 34.42 32.6208 33.98 33.2208C33.54 33.8208 33.34 34.5008 33.34 35.2808C33.34 35.5208 33.36 35.7408 33.4 35.9608C33.46 36.5208 33.66 36.9808 33.98 37.3608C34.42 37.9008 34.92 38.2808 35.5 38.5208C36.06 38.7808 36.78 39.0208 37.64 39.2608C38.3 39.4408 38.8 39.6008 39.12 39.7608C39.46 39.9008 39.68 40.0408 39.8 40.2008C39.94 40.3608 40 40.5608 40 40.8208C40 41.1608 39.82 41.4608 39.46 41.6608C39.1 41.8808 38.62 42.0008 38.02 42.0008C37.42 42.0008 36.92 41.8408 36.46 41.5608C36 41.2608 35.68 40.8208 35.46 40.2408L32.88 41.4408C33.3 42.4208 33.96 43.2008 34.86 43.8008C35.74 44.4008 36.8 44.7008 38 44.7008C38.98 44.7008 39.86 44.5408 40.62 44.2008C41.38 43.8808 41.98 43.4008 42.4 42.8008C42.82 42.1808 43.04 41.4608 43.04 40.6608C43.04 39.7808 42.82 39.0808 42.38 38.5608C41.94 38.0408 41.44 37.6608 40.86 37.4008C40.3 37.1608 39.58 36.9208 38.74 36.7008C38.08 36.5208 37.58 36.3608 37.26 36.2208C36.94 36.0808 36.7 35.9408 36.56 35.7608C36.56 35.7408 36.54 35.7408 36.54 35.7208C36.44 35.5808 36.38 35.3808 36.38 35.1408C36.38 34.8208 36.54 34.5408 36.88 34.3208Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
      {currentEffect === 9 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fillRule="evenodd" clipRule="evenodd" d="M41.74 26H26.24C26.84 28.34 28.48 30.26 30.64 31.26C29.72 32.24 29.02 33.4 28.56 34.68C27.62 34.22 26.78 33.64 26 32.94V44C26 45.12 25.1 46 24 46C17.36 46 12 40.64 12 34C12 30.92 13.16 28.12 15.06 26H4C2.88 26 2 25.12 2 24C2 17.38 7.36 12 14 12C17.06 12 19.86 13.18 22 15.08V4C22 2.9 22.88 2 24 2C30.62 2 36 7.38 36 14C36 17.08 34.82 19.88 32.92 22H44C45.1 22 46 22.9 46 24C46 26.28 45.36 28.4 44.26 30.22C43.22 29.38 42.02 28.74 40.7 28.38C41.16 27.66 41.52 26.86 41.74 26ZM14 16C10.26 16 7.14 18.56 6.24 22H21.74C20.86 18.56 17.74 16 14 16ZM16 34C16 37.74 18.54 40.86 22 41.76V26.26C18.54 27.14 16 30.26 16 34ZM32 14C32 10.28 29.44 7.14 26 6.26V21.76C29.44 20.86 32 17.72 32 14ZM40.54 34.0589L41.12 32.3789H42.58H44.32L39.52 45.3389H36.48L32.98 35.9389L31.66 32.3789H34.86L36.04 35.8189L37.94 41.2989H38.04L40.54 34.0589Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
      {currentEffect === 10 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M24.0032 16C19.5832 16 16.0032 19.58 16.0032 24C16.0032 28.42 19.5832 32 24.0032 32C28.4232 32 32.0032 28.42 32.0032 24C32.0032 19.58 28.4232 16 24.0032 16ZM24.0032 28C21.8032 28 20.0032 26.2 20.0032 24C20.0032 21.8 21.8032 20 24.0032 20C26.2032 20 28.0032 21.8 28.0032 24C28.0032 26.2 26.2032 28 24.0032 28Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M35.0032 19.2C36.3232 22.22 36.2432 25.56 35.0832 28.48C36.0032 28.2 36.9832 28 38.0032 28C39.4632 28 40.8632 28.34 42.1032 28.9C41.5832 24.48 40.2232 20.1 37.8632 16C33.4432 8.36 23.6432 5.74 16.0032 10.14C13.1832 11.78 11.0432 14.14 9.6832 16.88C9.4232 12.48 10.1232 8.08 11.7232 4H7.4832C4.4032 12.98 5.0432 23.16 10.1432 32C12.3432 35.82 15.9032 38.38 19.8632 39.44C22.5432 40.16 25.3832 40.16 28.1432 39.44C28.0832 38.96 28.0032 38.5 28.0032 38C28.0032 37 28.2032 36.04 28.4632 35.12C23.0032 37.3 16.6232 35.24 13.6032 30C13.3832 29.6 13.1832 29.2 13.0032 28.8C10.6032 23.28 12.6632 16.68 18.0032 13.6C23.7232 10.3 31.0832 12.26 34.4032 18C34.6232 18.4 34.8232 18.8 35.0032 19.2Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M40.8632 37.4C40.3032 37.16 39.5832 36.92 38.7432 36.7C38.0832 36.52 37.5832 36.36 37.2632 36.22C36.9432 36.08 36.7032 35.94 36.5632 35.76C36.5632 35.74 36.5432 35.74 36.5432 35.72C36.4432 35.58 36.3832 35.38 36.3832 35.14C36.3832 34.82 36.5432 34.54 36.8832 34.32C37.2232 34.1 37.6432 34 38.1632 34C38.6832 34 39.1832 34.12 39.5632 34.38C39.6232 34.42 39.7032 34.48 39.7632 34.54C40.0632 34.8 40.3232 35.16 40.5432 35.64L43.1032 34.44C42.8232 33.82 42.4832 33.3 42.0632 32.88C41.1032 31.82 39.8232 31.3 38.2032 31.3C37.7832 31.3 37.3632 31.34 36.9832 31.42C36.5832 31.58 36.1832 31.72 35.7432 31.8C35.0032 32.16 34.4232 32.62 33.9832 33.22C33.5432 33.82 33.3432 34.5 33.3432 35.28C33.3432 35.52 33.3632 35.74 33.4032 35.96C33.4632 36.52 33.6632 36.98 33.9832 37.36C34.4232 37.9 34.9232 38.28 35.5032 38.52C36.0632 38.78 36.7832 39.02 37.6432 39.26C38.3032 39.44 38.8032 39.6 39.1232 39.76C39.4632 39.9 39.6832 40.04 39.8032 40.2C39.9432 40.36 40.0032 40.56 40.0032 40.82C40.0032 41.16 39.8232 41.46 39.4632 41.66C39.1032 41.88 38.6232 42 38.0232 42C37.4232 42 36.9232 41.84 36.4632 41.56C36.0032 41.26 35.6832 40.82 35.4632 40.24L32.8832 41.44C33.3032 42.42 33.9632 43.2 34.8632 43.8C35.7432 44.4 36.8032 44.7 38.0032 44.7C38.9832 44.7 39.8632 44.54 40.6232 44.2C41.3832 43.88 41.9832 43.4 42.4032 42.8C42.8232 42.18 43.0432 41.46 43.0432 40.66C43.0432 39.78 42.8232 39.08 42.3832 38.56C41.9432 38.04 41.4432 37.66 40.8632 37.4Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
      {currentEffect === 11 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.6032 30C13.3832 29.6 13.1832 29.2 13.0032 28.8C10.6032 23.28 12.6632 16.68 18.0032 13.6C23.7232 10.3 31.0832 12.26 34.4032 18C34.6232 18.4 34.8232 18.8 35.0032 19.2C36.3032 22.2 36.3032 25.5 35.1632 28.4C36.0632 28.14 37.0032 28 38.0032 28C39.4832 28 40.8832 28.32 42.1432 28.9C41.6232 24.48 40.2232 20.1 37.8632 16C33.4432 8.36 23.6432 5.74 16.0032 10.14C13.1832 11.78 11.0432 14.14 9.6832 16.88C9.4232 12.48 10.1232 8.08 11.7232 4H7.4832C4.4032 12.98 5.0432 23.16 10.1432 32C12.3432 35.82 15.9032 38.38 19.8632 39.44C22.5232 40.16 25.3832 40.18 28.1232 39.48C28.0432 39 28.0032 38.5 28.0032 38C28.0032 37.02 28.1432 36.06 28.4032 35.16C22.9632 37.32 16.6232 35.22 13.6032 30Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M24.0032 16C19.5832 16 16.0032 19.58 16.0032 24C16.0032 28.42 19.5832 32 24.0032 32C28.4232 32 32.0032 28.42 32.0032 24C32.0032 19.58 28.4232 16 24.0032 16ZM24.0032 28C21.8032 28 20.0032 26.2 20.0032 24C20.0032 21.8 21.8032 20 24.0032 20C26.2032 20 28.0032 21.8 28.0032 24C28.0032 26.2 26.2032 28 24.0032 28Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M41.1232 32.28L38.0432 41.2H37.9432L37.5432 40.04L36.0432 35.72L35.7432 34.86L34.8632 32.28H31.6632L31.9432 33.02L32.9832 35.84L33.4032 36.94L36.4832 45.24H39.5232L39.9832 44L41.7832 39.12L44.3232 32.28H41.1232Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
      {currentEffect === 12 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M38 27C38.68 27 39.34 27.06 39.98 27.2C40 26.9 40 26.58 40 26.26C40 21.92 38.2 17.98 35.3 15.12L24 4L12.7 15.12C9.8 17.98 8 21.92 8 26.26C8 34.96 15.16 42 24 42C25.72 42 27.38 41.74 28.94 41.24C28.34 39.96 28 38.52 28 37C28 31.48 32.48 27 38 27ZM24 38C17.38 38 12 32.74 12 26.26C12 23.14 13.24 20.2 15.5 17.98L24 9.62V38Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M43.02 38.56C42.76 39.34 42.34 40.1 41.72 40.72C39.66 42.76 36.34 42.76 34.28 40.72L36.14 38.86H31.2V43.82L33.06 41.96C35.78 44.7 40.22 44.7 42.96 41.96C43.92 40.98 44.54 39.8 44.82 38.56H43.02Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M34.28 33.3C35.56 32.02 37.32 31.54 38.96 31.86C39.98 32.04 40.94 32.52 41.72 33.3L39.86 35.16H44.8V30.2L42.96 32.06C41.96 31.06 40.76 30.44 39.5 30.16C37.24 29.68 34.8 30.3 33.06 32.06C32.08 33.04 31.46 34.22 31.18 35.46H33C33.22 34.68 33.66 33.92 34.28 33.3Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
      {currentEffect === 13 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M24 4C12.96 4 4 12.96 4 24C4 35.04 12.96 44 24 44C35.04 44 44 35.04 44 24C44 12.96 35.04 4 24 4ZM24 40C15.18 40 8 32.82 8 24C8 15.18 15.18 8 24 8C32.82 8 40 15.18 40 24C40 32.82 32.82 40 24 40ZM30 13V18H22V22H30V27L37 20L30 13ZM18 21L11 28L18 35V30H26V26H18V21Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
      {currentEffect === 14 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M24 4C12.96 4 4 12.96 4 24C4 35.04 12.96 44 24 44C35.04 44 44 35.04 44 24C44 12.96 35.04 4 24 4ZM24 40C15.18 40 8 32.82 8 24C8 15.18 15.18 8 24 8C32.82 8 40 15.18 40 24C40 32.82 32.82 40 24 40ZM13 18L20 11L27 18H22V26H18V18H13ZM35 30L28 37L21 30H26V22H30V30H35Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
      {currentEffect === 15 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clipPath="url(#clip0_5305_23138)">
<rect width="48" height="48" fill="white" fillOpacity="0.01"/>
<path d="M30.5 10H21.5L31.5 24L21.5 38H30.5L40.5 24L30.5 10Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M16.5 10H7.5L17.5 24L7.5 38H16.5L26.5 24L16.5 10Z" fill={currentColor} fillOpacity="0.85"/>
</g>
<defs>
<clipPath id="clip0_5305_23138">
<rect width="48" height="48" fill="white"/>
</clipPath>
</defs>
</svg>
}

  {currentEffect === 16 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18 24C18 27.32 20.68 30 24 30C27.32 30 30 27.32 30 24C30 20.68 27.32 18 24 18C20.68 18 18 20.68 18 24ZM26 24C26 25.1 25.1 26 24 26C22.9 26 22 25.1 22 24C22 22.9 22.9 22 24 22C25.1 22 26 22.9 26 24Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M16 20V16H10.18C12.94 11.22 18.1 8 24 8C31.44 8 37.7 13.12 39.48 20H43.6C41.74 10.88 33.68 4 24 4C17.46 4 11.64 7.16 8 12.02V8H4V20H16Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M32 28V32H37.82C35.06 36.78 29.9 40 24 40C16.56 40 10.3 34.88 8.52 28H4.4C6.26 37.12 14.32 44 24 44C30.54 44 36.36 40.84 40 35.98V40H44V28H32Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}

{currentEffect === 17 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M24.02 18C20.7 18 18.02 20.68 18.02 24C18.02 27.32 20.7 30 24.02 30C27.34 30 30.02 27.32 30.02 24C30.02 20.68 27.34 18 24.02 18ZM24.02 26C22.92 26 22.02 25.1 22.02 24C22.02 22.9 22.92 22 24.02 22C25.12 22 26.02 22.9 26.02 24C26.02 25.1 25.12 26 24.02 26Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M16 16H10.18C12.94 11.22 18.1 8 24 8C31.44 8 37.7 13.12 39.48 20H43.6C41.74 10.88 33.68 4 24 4C17.46 4 11.64 7.16 8 12.02V8H4V20H16V16Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M24.02 40C16.58 40 10.32 34.88 8.54 28H4.4C6.26 37.12 14.32 44 24 44C25.9 44 27.74 43.74 29.48 43.22C28.78 42.1 28.3 40.82 28.12 39.46C26.82 39.82 25.42 40 24 40H24.02Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M43.9 34.56C43.38 33.58 42.62 32.84 41.62 32.32C40.62 31.8 39.44 31.54 38.06 31.54H33.44V44.5H37.8C39.24 44.5 40.48 44.24 41.52 43.72C42.56 43.2 43.34 42.44 43.88 41.48C44.42 40.5 44.7 39.36 44.7 38.04C44.7 36.72 44.44 35.56 43.92 34.6L43.9 34.56ZM40.64 40.72C39.96 41.38 39.02 41.7 37.8 41.7H36.4V34.3H37.92C39.1 34.3 40.02 34.62 40.66 35.28C41.3 35.94 41.62 36.84 41.62 38C41.62 39.16 41.28 40.06 40.62 40.72H40.64Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}

{currentEffect === 18 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M26.26 26H41.76C41.54 26.86 41.16 27.68 40.68 28.4C42 28.76 43.2 29.4 44.24 30.24C45.36 28.42 46 26.28 46 24C46 22.9 45.1 22 44 22H32.92C34.82 19.88 36 17.08 36 14C36 7.38 30.62 2 24 2C22.9 2 22 2.9 22 4V15.08C19.88 13.18 17.08 12 14 12C7.38 12 2 17.38 2 24C2 25.1 2.9 26 4 26H15.08C13.18 28.12 12 30.92 12 34C12 40.62 17.38 46 24 46C25.1 46 26 45.1 26 44V32.92C26.78 33.62 27.68 34.18 28.62 34.66C29.08 33.38 29.78 32.22 30.68 31.24C28.52 30.24 26.86 28.34 26.26 26ZM6.26 22C7.14 18.54 10.28 16 14 16C17.72 16 20.86 18.54 21.74 22H6.26ZM22 41.74C18.54 40.86 16 37.72 16 34C16 30.28 18.54 27.14 22 26.26V41.76V41.74ZM26 6.26C29.46 7.14 32 10.28 32 14C32 17.72 29.46 20.86 26 21.74V6.26Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M34.28 34.2789C36.32 32.2389 39.66 32.2389 41.7 34.2789L39.84 36.1389H44.78V31.1989L42.92 33.0589C40.18 30.3189 35.76 30.3189 33.02 33.0589C32.04 34.0389 31.42 35.2189 31.16 36.4589H32.96C33.2 35.6589 33.64 34.9189 34.26 34.2989L34.28 34.2789Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M41.72 41.7191C39.68 43.7591 36.34 43.7591 34.3 41.7191L36.16 39.8591H31.22V44.7991L33.08 42.9391C35.82 45.6791 40.24 45.6791 42.98 42.9391C43.96 41.9591 44.58 40.7791 44.84 39.5391H43.04C42.8 40.3391 42.36 41.0791 41.74 41.6991L41.72 41.7191Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}

{currentEffect === 19 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.6032 30C13.3832 29.6 13.1832 29.2 13.0032 28.8C10.6032 23.28 12.6632 16.68 18.0032 13.6C23.7232 10.3 31.0832 12.26 34.4032 18C34.6232 18.4 34.8232 18.8 35.0032 19.2C36.3232 22.22 36.2432 25.56 35.0832 28.48C36.0032 28.2 36.9832 28 38.0032 28C39.4632 28 40.8632 28.34 42.1032 28.9C41.5832 24.48 40.2232 20.1 37.8632 16C33.4432 8.36 23.6432 5.74 16.0032 10.14C13.1832 11.78 11.0432 14.14 9.6832 16.88C9.4232 12.48 10.1232 8.08 11.7232 4H7.48319C4.40319 12.98 5.0432 23.16 10.1432 32C12.3432 35.82 15.9032 38.38 19.8632 39.44C22.5432 40.16 25.3832 40.16 28.1432 39.44C28.0832 38.96 28.0032 38.5 28.0032 38C28.0032 37 28.2032 36.04 28.4632 35.12C23.0032 37.3 16.6232 35.24 13.6032 30Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M24.0032 16C19.5832 16 16.0032 19.58 16.0032 24C16.0032 28.42 19.5832 32 24.0032 32C28.4232 32 32.0032 28.42 32.0032 24C32.0032 19.58 28.4232 16 24.0032 16ZM24.0032 28C21.8032 28 20.0032 26.2 20.0032 24C20.0032 21.8 21.8032 20 24.0032 20C26.2032 20 28.0032 21.8 28.0032 24C28.0032 26.2 26.2032 28 24.0032 28Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M43.0232 39.54C42.7832 40.34 42.3432 41.08 41.7232 41.7C39.6832 43.74 36.3432 43.74 34.3032 41.7L36.1632 39.84H31.2232V44.78L33.0832 42.92C35.8232 45.66 40.2432 45.66 42.9832 42.92C43.9632 41.94 44.5832 40.76 44.8432 39.52H43.0432L43.0232 39.54Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M34.2832 34.28C36.3232 32.24 39.6632 32.24 41.7032 34.28L39.8432 36.14H44.7832V31.2L42.9232 33.06C40.1832 30.32 35.7632 30.32 33.0232 33.06C32.0432 34.04 31.4232 35.22 31.1632 36.46H32.9632C33.2032 35.66 33.6432 34.92 34.2632 34.3L34.2832 34.28Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}

{currentEffect === 20 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.08 16.92L4 24L11.08 31.08L14.6 27.54L11.08 24L14.6 20.46L11.08 16.92Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M27.54 33.4L24 36.92L20.46 33.4L16.92 36.92L24 44L28.18 39.82C28.06 39.22 28 38.62 28 38C28 36.76 28.22 35.58 28.64 34.48L27.54 33.4Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M16.92 11.08L20.46 14.6L24 11.08L27.54 14.6L31.08 11.08L24 4L16.92 11.08Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M34.5 28.64C35.6 28.22 36.76 28 38 28C38.62 28 39.22 28.06 39.82 28.18L44 24L36.92 16.92L33.4 20.46L36.92 24L33.4 27.54L34.5 28.64Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M24 30C27.3137 30 30 27.3137 30 24C30 20.6863 27.3137 18 24 18C20.6863 18 18 20.6863 18 24C18 27.3137 20.6863 30 24 30Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M43.9 34.56C43.38 33.58 42.62 32.84 41.62 32.32C40.62 31.8 39.44 31.54 38.06 31.54H33.44V44.5H37.8C39.24 44.5 40.48 44.24 41.52 43.72C42.56 43.2 43.34 42.44 43.88 41.48C44.42 40.5 44.7 39.36 44.7 38.04C44.7 36.72 44.44 35.56 43.92 34.6L43.9 34.56ZM40.64 40.72C39.96 41.38 39.02 41.7 37.8 41.7H36.4V34.3H37.92C39.1 34.3 40.02 34.62 40.66 35.28C41.3 35.94 41.62 36.84 41.62 38C41.62 39.16 41.28 40.06 40.62 40.72H40.64Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}

{currentEffect === 21 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.08 16.92L4 24L11.08 31.08L14.6 27.54L11.08 24L14.6 20.46L11.08 16.92ZM24 36.92L20.46 33.4L16.92 36.92L24 44L31.08 36.92L27.54 33.4L24 36.92ZM36.92 16.92L33.4 20.46L36.92 24L33.4 27.54L36.92 31.08L44 24L36.92 16.92ZM16.92 11.08L20.46 14.6L24 11.08L27.54 14.6L31.08 11.08L24 4L16.92 11.08Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M24 30C27.3137 30 30 27.3137 30 24C30 20.6863 27.3137 18 24 18C20.6863 18 18 20.6863 18 24C18 27.3137 20.6863 30 24 30Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}

{currentEffect === 22 &&
  <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M26.26 26H41.76C41.54 26.86 41.16 27.68 40.68 28.4C42 28.76 43.2 29.4 44.24 30.24C45.36 28.42 46 26.28 46 24C46 22.9 45.1 22 44 22H32.92C34.82 19.88 36 17.08 36 14C36 7.38 30.62 2 24 2C22.9 2 22 2.9 22 4V15.08C19.88 13.18 17.08 12 14 12C7.38 12 2 17.38 2 24C2 25.1 2.9 26 4 26H15.08C13.18 28.12 12 30.92 12 34C12 40.62 17.38 46 24 46C25.1 46 26 45.1 26 44V32.92C26.78 33.62 27.68 34.18 28.62 34.66C29.08 33.38 29.78 32.22 30.68 31.24C28.52 30.24 26.86 28.34 26.26 26ZM6.26 22C7.14 18.54 10.28 16 14 16C17.72 16 20.86 18.54 21.74 22H6.26ZM22 41.74C18.54 40.86 16 37.72 16 34C16 30.28 18.54 27.14 22 26.26V41.76V41.74ZM26 6.26C29.46 7.14 32 10.28 32 14C32 17.72 29.46 20.86 26 21.74V6.26Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M42.3 38.46H41.74V39.38H42.3C42.6 39.38 42.84 39.34 43.02 39.26C43.18 39.18 43.28 39.06 43.28 38.9C43.28 38.76 43.2 38.64 43.02 38.58C42.86 38.5 42.62 38.46 42.3 38.46Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M41.74 36.62V37.48H42.24C42.5 37.48 42.7 37.44 42.84 37.36C42.98 37.28 43.04 37.18 43.04 37.02C43.04 36.88 42.98 36.78 42.84 36.72C42.7 36.64 42.5 36.62 42.24 36.62H41.72H41.74Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M33.52 36.62H33.08V37.76H33.52C33.82 37.76 34.04 37.72 34.2 37.62C34.36 37.52 34.44 37.38 34.44 37.2C34.44 37.02 34.36 36.88 34.2 36.78C34.04 36.68 33.82 36.64 33.52 36.64V36.62Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M38 30C33.58 30 30 33.58 30 38C30 42.42 33.58 46 38 46C42.42 46 46 42.42 46 38C46 33.58 42.42 30 38 30ZM34.5 40.34L33.7 38.74H33.08V40.34H32V35.64H33.68C34.06 35.64 34.4 35.7 34.68 35.82C34.96 35.94 35.18 36.12 35.32 36.36C35.46 36.6 35.54 36.86 35.54 37.18C35.54 37.5 35.48 37.76 35.34 37.98C35.2 38.2 35.02 38.38 34.76 38.52L35.78 40.32H34.52L34.5 40.34ZM37.38 39.06C37.56 39.3 37.82 39.42 38.16 39.42C38.32 39.42 38.46 39.4 38.58 39.34C38.7 39.3 38.82 39.2 38.92 39.1V38.58H37.94V37.6H39.94V39.5C39.7 39.82 39.44 40.04 39.14 40.2C38.84 40.36 38.52 40.42 38.16 40.42C37.74 40.42 37.36 40.32 37.02 40.12C36.68 39.92 36.44 39.64 36.26 39.28C36.08 38.92 36 38.48 36 38C36 37.52 36.1 37.08 36.28 36.72C36.46 36.36 36.72 36.06 37.04 35.88C37.38 35.68 37.76 35.58 38.2 35.58C38.6 35.58 38.96 35.68 39.26 35.86C39.56 36.04 39.8 36.3 39.98 36.64L39.14 37.24C39.04 37.04 38.9 36.88 38.74 36.78C38.56 36.66 38.4 36.6 38.2 36.6C37.86 36.6 37.58 36.72 37.4 36.98C37.2 37.22 37.12 37.58 37.12 38.02C37.12 38.46 37.22 38.82 37.4 39.08L37.38 39.06ZM44.16 36.86C44.16 37.4 43.88 37.76 43.32 37.92V37.96C43.68 38.02 43.94 38.12 44.12 38.3C44.3 38.48 44.38 38.72 44.38 39.02C44.38 39.44 44.22 39.78 43.88 40C43.54 40.22 43.06 40.34 42.44 40.34H40.66V35.64H42.38C42.96 35.64 43.4 35.74 43.7 35.96C44 36.18 44.16 36.48 44.16 36.86Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}

{currentEffect === 23 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20 24C17.8 24 16 25.8 16 28C16 30.2 17.8 32 20 32C22.2 32 24 30.2 24 28C24 25.8 22.2 24 20 24ZM12 16C9.8 16 8 17.8 8 20C8 22.2 9.8 24 12 24C14.2 24 16 22.2 16 20C16 17.8 14.2 16 12 16ZM12 32C9.8 32 8 33.8 8 36C8 38.2 9.8 40 12 40C14.2 40 16 38.2 16 36C16 33.8 14.2 32 12 32ZM36 16C38.2 16 40 14.2 40 12C40 9.8 38.2 8 36 8C33.8 8 32 9.8 32 12C32 14.2 33.8 16 36 16ZM28 32C25.8 32 24 33.8 24 36C24 38.2 25.8 40 28 40C30.2 40 32 38.2 32 36C32 33.8 30.2 32 28 32ZM36 24C33.8 24 32 25.8 32 28C32 30.2 33.8 32 36 32C38.2 32 40 30.2 40 28C40 25.8 38.2 24 36 24ZM28 16C25.8 16 24 17.8 24 20C24 22.2 25.8 24 28 24C30.2 24 32 22.2 32 20C32 17.8 30.2 16 28 16ZM20 8C17.8 8 16 9.8 16 12C16 14.2 17.8 16 20 16C22.2 16 24 14.2 24 12C24 9.8 22.2 8 20 8Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}

{currentEffect === 24 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20 16C22.2091 16 24 14.2091 24 12C24 9.79086 22.2091 8 20 8C17.7909 8 16 9.79086 16 12C16 14.2091 17.7909 16 20 16Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M28 24C30.2091 24 32 22.2091 32 20C32 17.7909 30.2091 16 28 16C25.7909 16 24 17.7909 24 20C24 22.2091 25.7909 24 28 24Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M36 16C38.2091 16 40 14.2091 40 12C40 9.79086 38.2091 8 36 8C33.7909 8 32 9.79086 32 12C32 14.2091 33.7909 16 36 16Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M36 24C33.8 24 32 25.8 32 28C32 28.62 32.16 29.2 32.4 29.72C34 28.64 35.92 28 38 28C38.68 28 39.34 28.08 39.98 28.2C39.98 28.14 40 28.06 40 28C40 25.8 38.2 24 36 24Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M20 32C22.2091 32 24 30.2091 24 28C24 25.7909 22.2091 24 20 24C17.7909 24 16 25.7909 16 28C16 30.2091 17.7909 32 20 32Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M28 32C25.8 32 24 33.8 24 36C24 38.2 25.8 40 28 40C28.06 40 28.14 40 28.2 39.98C28.08 39.34 28 38.68 28 38C28 35.92 28.64 34 29.72 32.4C29.2 32.14 28.62 32 28 32Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M12 24C14.2091 24 16 22.2091 16 20C16 17.7909 14.2091 16 12 16C9.79086 16 8 17.7909 8 20C8 22.2091 9.79086 24 12 24Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M12 40C14.2091 40 16 38.2091 16 36C16 33.7909 14.2091 32 12 32C9.79086 32 8 33.7909 8 36C8 38.2091 9.79086 40 12 40Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M43.02 38.58C42.86 38.5 42.62 38.46 42.3 38.46H41.74V39.38H42.3C42.6 39.38 42.84 39.34 43.02 39.26C43.18 39.18 43.28 39.06 43.28 38.9C43.28 38.76 43.2 38.64 43.02 38.58Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M42.84 37.36C42.98 37.28 43.04 37.18 43.04 37.02C43.04 36.88 42.98 36.78 42.84 36.72C42.7 36.64 42.5 36.62 42.24 36.62H41.72V37.48H42.22C42.48 37.48 42.68 37.44 42.82 37.36H42.84Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M33.52 36.62H33.08V37.76H33.52C33.82 37.76 34.04 37.72 34.2 37.62C34.36 37.52 34.44 37.38 34.44 37.2C34.44 37.02 34.36 36.88 34.2 36.78C34.04 36.68 33.82 36.64 33.52 36.64V36.62Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M38 30C33.58 30 30 33.58 30 38C30 42.42 33.58 46 38 46C42.42 46 46 42.42 46 38C46 33.58 42.42 30 38 30ZM34.5 40.34L33.7 38.74H33.08V40.34H32V35.64H33.68C34.06 35.64 34.4 35.7 34.68 35.82C34.96 35.94 35.18 36.12 35.32 36.36C35.46 36.6 35.54 36.86 35.54 37.18C35.54 37.5 35.48 37.76 35.34 37.98C35.2 38.2 35.02 38.38 34.76 38.52L35.78 40.32H34.52L34.5 40.34ZM37.38 39.06C37.56 39.3 37.82 39.42 38.16 39.42C38.32 39.42 38.46 39.4 38.58 39.34C38.7 39.3 38.82 39.2 38.92 39.1V38.58H37.94V37.6H39.94V39.5C39.7 39.82 39.44 40.04 39.14 40.2C38.84 40.36 38.52 40.42 38.16 40.42C37.74 40.42 37.36 40.32 37.02 40.12C36.68 39.92 36.44 39.64 36.26 39.28C36.08 38.92 36 38.48 36 38C36 37.52 36.1 37.08 36.28 36.72C36.46 36.36 36.72 36.06 37.04 35.88C37.38 35.68 37.76 35.58 38.2 35.58C38.6 35.58 38.96 35.68 39.26 35.86C39.56 36.04 39.8 36.3 39.98 36.64L39.14 37.24C39.04 37.04 38.9 36.88 38.74 36.78C38.56 36.66 38.4 36.6 38.2 36.6C37.86 36.6 37.58 36.72 37.4 36.98C37.2 37.22 37.12 37.58 37.12 38.02C37.12 38.46 37.22 38.82 37.4 39.08L37.38 39.06ZM42.42 40.34H40.64V35.64H42.36C42.94 35.64 43.38 35.74 43.68 35.96C43.98 36.18 44.14 36.48 44.14 36.86C44.14 37.4 43.86 37.76 43.3 37.92V37.96C43.66 38.02 43.92 38.12 44.1 38.3C44.28 38.48 44.36 38.72 44.36 39.02C44.36 39.44 44.2 39.78 43.86 40C43.52 40.22 43.04 40.34 42.42 40.34Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}

{currentEffect === 25 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M24 4C12.96 4 4 12.96 4 24C4 35.04 12.96 44 24 44C35.04 44 44 35.04 44 24C44 12.96 35.04 4 24 4ZM24 40C15.18 40 8 32.82 8 24C8 15.18 15.18 8 24 8C32.82 8 40 15.18 40 24C40 32.82 32.82 40 24 40ZM24 15C19.02 15 15 19.02 15 24C15 28.98 19.02 33 24 33C28.98 33 33 28.98 33 24C33 19.02 28.98 15 24 15ZM24 26C22.9 26 22 25.1 22 24C22 22.9 22.9 22 24 22C25.1 22 26 22.9 26 24C26 25.1 25.1 26 24 26Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}

{currentEffect === 26 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M24 35.48C27.3137 35.48 30 30.3402 30 24C30 17.6598 27.3137 12.52 24 12.52C20.6863 12.52 18 17.6598 18 24C18 30.3402 20.6863 35.48 24 35.48Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M15 24C15 22.06 15.46 15.68 21.06 11C19.5 10.38 17.8 10 16 10C8.28 10 2 16.28 2 24C2 31.72 8.28 38 16 38C17.8 38 19.5 37.62 21.06 37C15.46 32.32 15 25.94 15 24Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M32 10C30.2 10 28.5 10.38 26.94 11C28.16 12.02 29.14 13.14 29.92 14.26C30.58 14.1 31.28 14 32 14C37.52 14 42 18.48 42 24C42 29.52 37.52 34 32 34C31.28 34 30.58 33.9 29.92 33.74C29.14 34.86 28.16 35.98 26.94 37C28.5 37.62 30.2 38 32 38C39.72 38 46 31.72 46 24C46 16.28 39.72 10 32 10Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 27 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.7 25C13.08 25 13.54 27 17.36 27C21.26 27 21.54 25 24.02 25C26.4 25 26.86 27 30.68 27C34.58 27 34.86 25 37.34 25C39.72 25 40.14 26.96 43.96 27V23C41.58 23 41.12 21 37.3 21C33.4 21 33.12 23 30.64 23C28.26 23 27.8 21 23.98 21C20.08 21 19.8 23 17.32 23C14.94 23 14.48 21 10.66 21C6.76 21 6.48 23 4 23V27C7.8 27 8.34 25 10.7 25ZM37.34 29C33.44 29 33.16 31 30.68 31C28.3 31 27.84 29 24.02 29C20.12 29 19.82 31 17.34 31C14.86 31 14.58 29 10.68 29C6.78 29 6.48 31 4 31V35C7.9 35 8.22 33 10.68 33C13.16 33 13.44 35 17.34 35C21.24 35 21.54 33 24.02 33C26.4 33 26.86 35 30.68 35C34.56 35 34.86 33 37.34 33C39.72 33 40.18 35 44 35V31C41.52 31 41.24 29 37.34 29ZM10.7 17C13.08 17 13.54 19 17.36 19C21.26 19 21.54 17 24.02 17C26.4 17 26.86 19 30.68 19C34.58 19 34.86 17 37.34 17C39.72 17 40.14 18.96 43.96 19V15C41.58 15 41.12 13 37.3 13C33.4 13 33.12 15 30.64 15C28.26 15 27.8 13 23.98 13C20.08 13 19.8 15 17.32 15C14.94 15 14.48 13 10.66 13C6.76 13 6.48 15 4 15V19C7.8 19 8.34 17 10.7 17Z" fill={currentColor} fillOpacity="0.85"/>
</svg>}
{currentEffect === 28 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M39.76 27.78C41.84 27.78 43.54 26.08 43.54 24C43.54 21.92 41.84 20.22 39.76 20.22C37.68 20.22 35.98 21.92 35.98 24C35.98 26.08 37.68 27.78 39.76 27.78ZM31.28 27.36C33.14 27.36 34.64 25.86 34.64 24C34.64 22.14 33.14 20.64 31.28 20.64C29.42 20.64 27.92 22.14 27.92 24C27.92 25.86 29.42 27.36 31.28 27.36ZM31.28 18.84C33.14 18.84 34.64 17.34 34.64 15.48C34.64 13.62 33.14 12.12 31.28 12.12C29.42 12.12 27.92 13.62 27.92 15.48C27.92 17.34 29.42 18.84 31.28 18.84ZM31.28 35.86C33.14 35.86 34.64 34.36 34.64 32.5C34.64 30.64 33.14 29.14 31.28 29.14C29.42 29.14 27.92 30.64 27.92 32.5C27.92 34.34 29.42 35.86 31.28 35.86ZM22.8 26.94C24.42 26.94 25.74 25.62 25.74 24C25.74 22.38 24.42 21.06 22.8 21.06C21.18 21.06 19.86 22.38 19.86 24C19.86 25.62 21.16 26.94 22.8 26.94ZM22.8 18.42C24.42 18.42 25.74 17.1 25.74 15.48C25.74 13.86 24.42 12.54 22.8 12.54C21.18 12.54 19.86 13.86 19.86 15.48C19.86 17.1 21.16 18.42 22.8 18.42ZM22.8 35.46C24.42 35.46 25.74 34.14 25.74 32.52C25.74 30.9 24.42 29.58 22.8 29.58C21.18 29.58 19.86 30.9 19.86 32.52C19.84 34.14 21.16 35.46 22.8 35.46ZM22.8 44C24.42 44 25.74 42.68 25.74 41.06C25.74 39.44 24.42 38.12 22.8 38.12C21.18 38.12 19.86 39.44 19.86 41.06C19.84 42.68 21.16 44 22.8 44ZM22.8 9.88C24.42 9.88 25.74 8.56 25.74 6.94C25.74 5.32 24.42 4 22.8 4C21.18 4 19.86 5.32 19.86 6.94C19.86 8.56 21.16 9.88 22.8 9.88ZM14.3 26.52C15.7 26.52 16.82 25.38 16.82 24C16.82 22.62 15.68 21.48 14.3 21.48C12.9 21.48 11.78 22.62 11.78 24C11.78 25.38 12.9 26.52 14.3 26.52ZM14.3 35.02C15.7 35.02 16.82 33.88 16.82 32.5C16.82 31.1 15.68 29.98 14.3 29.98C12.9 29.98 11.78 31.12 11.78 32.5C11.78 33.88 12.9 35.02 14.3 35.02ZM14.3 18.04C15.7 18.04 16.82 16.9 16.82 15.52C16.82 14.12 15.68 13 14.3 13C12.9 13 11.78 14.14 11.78 15.52C11.78 16.9 12.9 18.04 14.3 18.04ZM6.55996 26.1C7.71996 26.1 8.65996 25.16 8.65996 24C8.65996 22.84 7.71996 21.9 6.55996 21.9C5.39996 21.9 4.45996 22.84 4.45996 24C4.45996 25.16 5.39996 26.1 6.55996 26.1Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 29 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14 36H18V12H14V36ZM22 44H26V4H22V44ZM6 28H10V20H6V28ZM30 36H34V12H30V36ZM38 20V28H42V20H38Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 30 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M26 14H22V18H26V14ZM26 22H22V26H26V22ZM34 22H30V26H34V22ZM6 6V42H42V6H6ZM38 38H10V10H38V38ZM26 30H22V34H26V30ZM18 22H14V26H18V22Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 31 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12 6H6V12C9.32 12 12 9.32 12 6ZM28 6H24C24 15.94 15.94 24 6 24V28C18.16 28 28 18.14 28 6ZM20 6H16C16 11.52 11.52 16 6 16V20C13.74 20 20 13.74 20 6ZM20 42H24C24 32.06 32.06 24 42 24V20C29.86 20 20 29.86 20 42ZM36 42H42V36C38.68 36 36 38.68 36 42ZM28 42H32C32 36.48 36.48 32 42 32V28C34.26 28 28 34.26 28 42Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 32 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12 26C10.9 26 10 26.9 10 28C10 29.1 10.9 30 12 30C13.1 30 14 29.1 14 28C14 26.9 13.1 26 12 26ZM12 34C10.9 34 10 34.9 10 36C10 37.1 10.9 38 12 38C13.1 38 14 37.1 14 36C14 34.9 13.1 34 12 34ZM12 18C10.9 18 10 18.9 10 20C10 21.1 10.9 22 12 22C13.1 22 14 21.1 14 20C14 18.9 13.1 18 12 18ZM6 19C5.44 19 5 19.44 5 20C5 20.56 5.44 21 6 21C6.56 21 7 20.56 7 20C7 19.44 6.56 19 6 19ZM12 10C10.9 10 10 10.9 10 12C10 13.1 10.9 14 12 14C13.1 14 14 13.1 14 12C14 10.9 13.1 10 12 10ZM42 21C42.56 21 43 20.56 43 20C43 19.44 42.56 19 42 19C41.44 19 41 19.44 41 20C41 20.56 41.44 21 42 21ZM28 14C29.1 14 30 13.1 30 12C30 10.9 29.1 10 28 10C26.9 10 26 10.9 26 12C26 13.1 26.9 14 28 14ZM28 7C28.56 7 29 6.56 29 6C29 5.44 28.56 5 28 5C27.44 5 27 5.44 27 6C27 6.56 27.44 7 28 7ZM6 27C5.44 27 5 27.44 5 28C5 28.56 5.44 29 6 29C6.56 29 7 28.56 7 28C7 27.44 6.56 27 6 27ZM20 41C19.44 41 19 41.44 19 42C19 42.56 19.44 43 20 43C20.56 43 21 42.56 21 42C21 41.44 20.56 41 20 41ZM20 7C20.56 7 21 6.56 21 6C21 5.44 20.56 5 20 5C19.44 5 19 5.44 19 6C19 6.56 19.44 7 20 7ZM20 14C21.1 14 22 13.1 22 12C22 10.9 21.1 10 20 10C18.9 10 18 10.9 18 12C18 13.1 18.9 14 20 14ZM20 25C18.34 25 17 26.34 17 28C17 29.66 18.34 31 20 31C21.66 31 23 29.66 23 28C23 26.34 21.66 25 20 25ZM36 26C34.9 26 34 26.9 34 28C34 29.1 34.9 30 36 30C37.1 30 38 29.1 38 28C38 26.9 37.1 26 36 26ZM36 34C34.9 34 34 34.9 34 36C34 37.1 34.9 38 36 38C37.1 38 38 37.1 38 36C38 34.9 37.1 34 36 34ZM36 18C34.9 18 34 18.9 34 20C34 21.1 34.9 22 36 22C37.1 22 38 21.1 38 20C38 18.9 37.1 18 36 18ZM36 10C34.9 10 34 10.9 34 12C34 13.1 34.9 14 36 14C37.1 14 38 13.1 38 12C38 10.9 37.1 10 36 10ZM42 27C41.44 27 41 27.44 41 28C41 28.56 41.44 29 42 29C42.56 29 43 28.56 43 28C43 27.44 42.56 27 42 27ZM28 34C26.9 34 26 34.9 26 36C26 37.1 26.9 38 28 38C29.1 38 30 37.1 30 36C30 34.9 29.1 34 28 34ZM28 41C27.44 41 27 41.44 27 42C27 42.56 27.44 43 28 43C28.56 43 29 42.56 29 42C29 41.44 28.56 41 28 41ZM20 17C18.34 17 17 18.34 17 20C17 21.66 18.34 23 20 23C21.66 23 23 21.66 23 20C23 18.34 21.66 17 20 17ZM20 34C18.9 34 18 34.9 18 36C18 37.1 18.9 38 20 38C21.1 38 22 37.1 22 36C22 34.9 21.1 34 20 34ZM28 25C26.34 25 25 26.34 25 28C25 29.66 26.34 31 28 31C29.66 31 31 29.66 31 28C31 26.34 29.66 25 28 25ZM28 17C26.34 17 25 18.34 25 20C25 21.66 26.34 23 28 23C29.66 23 31 21.66 31 20C31 18.34 29.66 17 28 17Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 33 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M37.9935 18H9.99348V30H31.9935C33.6535 28.74 35.7335 28 37.9935 28V18ZM33.9935 26H13.9935V22H33.9935V26Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0135 4H22.0135V10H26.0135V4Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M38.1672 9.19257L34.5877 12.7972L37.426 15.6157L41.0055 12.0111L38.1672 9.19257Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0135 38H22.0135V44H26.0135V38Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M9.82823 9.20083L7.01394 12.0151L10.5919 15.5931L13.4062 12.7788L9.82823 9.20083Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M10.6046 32.5723L7 36.1519L9.81854 38.9902L13.4232 35.4106L10.6046 32.5723Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M40.8735 37.4C40.2935 37.16 39.5935 36.92 38.7535 36.7C38.0935 36.52 37.5935 36.36 37.2735 36.22C36.9535 36.08 36.7135 35.92 36.5935 35.76C36.4535 35.6 36.3935 35.38 36.3935 35.12C36.3935 34.8 36.5535 34.54 36.9135 34.32C37.2535 34.1 37.6735 34 38.1935 34C38.7335 34 39.1935 34.12 39.5935 34.38C39.9735 34.64 40.3135 35.06 40.5935 35.64L43.1535 34.44C42.1535 32.34 40.5335 31.28 38.2335 31.28C37.3335 31.28 36.5135 31.46 35.7735 31.8C35.0335 32.14 34.4535 32.62 34.0135 33.22C33.5735 33.82 33.3735 34.5 33.3735 35.28C33.3735 36.14 33.5935 36.84 34.0335 37.36C34.4735 37.9 34.9735 38.28 35.5535 38.52C36.1335 38.76 36.8335 39.02 37.7135 39.26C38.3735 39.44 38.8535 39.6 39.1935 39.74C39.5335 39.88 39.7535 40.04 39.8735 40.2C39.9935 40.36 40.0735 40.56 40.0735 40.8C40.0735 41.16 39.8935 41.44 39.5335 41.66C39.1735 41.88 38.6935 41.98 38.0935 41.98C37.4935 41.98 36.9935 41.84 36.5335 41.54C36.0735 41.24 35.7535 40.8 35.5335 40.22L32.9535 41.42C33.3735 42.4 34.0335 43.18 34.9335 43.78C35.8335 44.38 36.8735 44.68 38.0735 44.68C39.0535 44.68 39.9335 44.52 40.6935 44.18C41.4535 43.84 42.0535 43.38 42.4735 42.76C42.8935 42.14 43.1135 41.44 43.1135 40.62C43.1135 39.74 42.8935 39.04 42.4535 38.52C42.0135 38 41.5135 37.62 40.9335 37.36L40.8735 37.4Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 34 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.0079 30H38.0079V18H10.0079V30ZM14.0079 22H34.0079V26H14.0079V22Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0079 4H22.0079V10H26.0079V4Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M38.16 9.19297L34.5806 12.7977L37.419 15.6161L40.9984 12.0114L38.16 9.19297Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0079 38H22.0079V44H26.0079V38Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M34.5879 35.42L38.1679 39.02L41.0079 36.18L37.4079 32.6L34.5879 35.42Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M9.81155 9.20393L6.99729 12.0182L10.5752 15.5961L13.3895 12.7819L9.81155 9.20393Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M10.597 32.5731L6.99219 36.1525L9.81059 38.9909L13.4154 35.4115L10.597 32.5731Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 35 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.99348 30H31.9935C33.6535 28.74 35.7335 28 37.9935 28V18H9.99348V30ZM13.9935 22H33.9935V26H13.9935V22Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0135 4H22.0135V10H26.0135V4Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M38.1672 9.19257L34.5877 12.7972L37.426 15.6157L41.0055 12.0111L38.1672 9.19257Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0135 38H22.0135V44H26.0135V38Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M9.82823 9.20083L7.01394 12.0151L10.5919 15.5931L13.4062 12.7788L9.82823 9.20083Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M10.6046 32.5723L7 36.1519L9.81854 38.9902L13.4232 35.4106L10.6046 32.5723Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M42.9935 33.28L41.3535 40.28H41.2735L39.2535 33.28H36.6535L34.7535 40.28H34.6735L33.0335 33.28H30.5135L33.2335 43.8H35.7135L37.9135 36.9H37.9735L40.1735 43.8H42.6535L45.5135 33.28H42.9935Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 36 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.0198 30H32.0398C33.6998 28.74 35.7598 28 37.9998 28H38.0198V18H10.0198V30ZM14.0198 22H34.0198V26H14.0198V22Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0198 4H22.0198V10H26.0198V4Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M38.1736 9.19257L34.594 12.7972L37.4323 15.6157L41.0119 12.0111L38.1736 9.19257Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0198 38H22.0198V44H26.0198V38Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M9.83458 9.20083L7.02029 12.0151L10.5983 15.5931L13.4125 12.7788L9.83458 9.20083Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M10.611 32.5723L7.00635 36.1519L9.82488 38.9902L13.4295 35.4106L10.611 32.5723Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M41.6998 32.18L38.0598 37.64H37.9598L34.2998 32.18H31.9398V43.82H34.5198V37.2H34.6198L36.9998 40.56H39.0198L41.3998 37.14H41.4998V43.82H44.0798V32.18H41.6998Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 37 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M37.9998 18H9.99983V30H31.9998C33.6598 28.74 35.7398 28 37.9998 28V18ZM33.9998 26H13.9998V22H33.9998V26Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0198 4H22.0198V10H26.0198V4Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M38.1736 9.19257L34.594 12.7972L37.4323 15.6157L41.0119 12.0111L38.1736 9.19257Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0198 38H22.0198V44H26.0198V38Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M9.83458 9.20083L7.02029 12.0151L10.5983 15.5931L13.4125 12.7788L9.83458 9.20083Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M10.611 32.5723L7.00635 36.1519L9.82488 38.9902L13.4295 35.4106L10.611 32.5723Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M40.8798 40.34C40.3198 41.4 39.5198 41.92 38.5198 41.92C37.9398 41.92 37.4198 41.76 36.9798 41.4C36.5198 41.06 36.1798 40.58 35.9198 39.98C35.6798 39.38 35.5398 38.7 35.5398 37.92C35.5398 37.14 35.6598 36.5 35.9198 35.92C36.1798 35.34 36.5198 34.88 36.9798 34.56C37.4398 34.22 37.9398 34.06 38.5198 34.06C39.0598 34.06 39.5398 34.2 39.9398 34.46C40.3398 34.72 40.6398 35.12 40.8598 35.64L43.4998 34.48C42.4798 32.34 40.8798 31.26 38.6998 31.26C37.4798 31.26 36.3998 31.54 35.4598 32.1C34.5198 32.66 33.7998 33.46 33.2598 34.46C32.7198 35.46 32.4798 36.62 32.4798 37.9C32.4798 39.18 32.7398 40.36 33.2598 41.4C33.7798 42.42 34.4998 43.24 35.4198 43.8C36.3398 44.38 37.3798 44.66 38.5398 44.66C39.6998 44.66 40.6598 44.38 41.5198 43.82C42.3798 43.26 43.0398 42.48 43.5198 41.44L40.8798 40.28V40.34Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 38 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.0135 30H32.0335C33.6935 28.74 35.7735 28 38.0135 28V18H10.0135V30ZM14.0135 22H34.0135V26H14.0135V22Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0135 4H22.0135V10H26.0135V4Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M38.1672 9.19257L34.5877 12.7972L37.426 15.6157L41.0055 12.0111L38.1672 9.19257Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0135 38H22.0135V44H26.0135V38Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M9.82823 9.20083L7.01394 12.0151L10.5919 15.5931L13.4062 12.7788L9.82823 9.20083Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M10.6046 32.5723L7 36.1519L9.81854 38.9902L13.4232 35.4106L10.6046 32.5723Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M34.5335 37.76H34.4735L31.9335 33.94H30.2935V42.04H32.0935V37.44H32.1535L33.8135 39.78H35.2135L36.8735 37.4H36.9335V42.04H38.7335V33.94H37.0735L34.5335 37.76Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M45.0335 39.46C44.6735 40.12 44.1935 40.46 43.5535 40.46C43.1935 40.46 42.8735 40.36 42.5935 40.14C42.3135 39.92 42.0935 39.64 41.9335 39.26C41.7735 38.88 41.6935 38.46 41.6935 37.98C41.6935 37.5 41.7735 37.1 41.9335 36.74C42.0935 36.38 42.3135 36.1 42.5935 35.88C42.8735 35.66 43.1935 35.56 43.5535 35.56C43.8935 35.56 44.1935 35.64 44.4335 35.8C44.6735 35.96 44.8735 36.22 45.0135 36.54L46.6535 35.82C46.0135 34.48 45.0135 33.82 43.6535 33.82C42.8935 33.82 42.2135 34 41.6335 34.34C41.0535 34.7 40.5935 35.18 40.2535 35.82C39.9335 36.46 39.7535 37.16 39.7535 37.96C39.7535 38.76 39.9135 39.5 40.2335 40.14C40.5535 40.78 41.0135 41.28 41.5735 41.64C42.1535 42 42.7935 42.18 43.5335 42.18C44.2735 42.18 44.8535 42 45.3935 41.66C45.9335 41.32 46.3535 40.82 46.6535 40.16L44.9935 39.44L45.0335 39.46Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 39 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M37.9935 18H9.99348V30H31.9935C33.6535 28.74 35.7335 28 37.9935 28V18ZM33.9935 26H13.9935V22H33.9935V26Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0135 4H22.0135V10H26.0135V4Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M38.1672 9.19257L34.5877 12.7972L37.426 15.6157L41.0055 12.0111L38.1672 9.19257Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0135 38H22.0135V44H26.0135V38Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M9.82823 9.20083L7.01394 12.0151L10.5919 15.5931L13.4062 12.7788L9.82823 9.20083Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M10.6046 32.5723L7 36.1519L9.81854 38.9902L13.4232 35.4106L10.6046 32.5723Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M40.3535 31.52V38.86H40.2535L35.3935 31.52H32.6735V44.48H35.6535V37.14H35.7535L40.7535 44.48H43.3135V31.52H40.3535Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 40 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.0135 30H32.0335C33.6935 28.74 35.7735 28 38.0135 28V18H10.0135V30ZM14.0135 22H34.0135V26H14.0135V22Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0135 4H22.0135V10H26.0135V4Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M38.1672 9.19257L34.5877 12.7972L37.426 15.6157L41.0055 12.0111L38.1672 9.19257Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M26.0135 38H22.0135V44H26.0135V38Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M9.82823 9.20083L7.01394 12.0151L10.5919 15.5931L13.4062 12.7788L9.82823 9.20083Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M10.6046 32.5723L7 36.1519L9.81854 38.9902L13.4232 35.4106L10.6046 32.5723Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M34.5335 37.76H34.4735L31.9335 33.96H30.2935V42.04H32.0935V37.44H32.1535L33.8135 39.78H35.2135L36.8735 37.4H36.9335V42.04H38.7335V33.96H37.0735L34.5335 37.76Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M44.7935 33.96V38.54H44.7335L41.7135 33.96H40.0135V42.04H41.8735V37.46H41.9335L45.0535 42.04H46.6535V33.96H44.7935Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 41 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M30 6L34.6 10.6L28.82 16.34L31.66 19.18L37.4 13.4L42 18V6H30ZM6 18L10.6 13.4L16.34 19.18L19.18 16.34L13.4 10.6L18 6H6V18ZM18 42L13.4 37.4L19.18 31.66L16.34 28.82L10.6 34.6L6 30V42H18ZM42 30L37.4 34.6L31.66 28.82L28.82 31.66L34.6 37.4L30 42H42V30Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 42 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M31.66 19.18L37.4 13.4L42 18V6H30L34.6 10.6L28.82 16.34L31.66 19.18Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M31.66 28.82L28.82 31.66L29.66 32.5C30.4 31.38 31.36 30.42 32.5 29.66L31.66 28.82Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M16.34 28.82L10.6 34.6L6 30V42H18L13.4 37.4L19.18 31.66L16.34 28.82Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M18 6H6V18L10.6 13.4L16.34 19.18L19.18 16.34L13.4 10.6L18 6Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M41.7 32.18L38.04 37.64H37.96L34.3 32.18H31.92V43.82H34.52V37.2H34.6L37 40.56H39L41.4 37.14H41.48V43.82H44.08V32.18H41.7Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 43 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M30 6L34.6 10.6L28.82 16.34L31.66 19.18L37.4 13.4L42 18V6H30Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M18 6H6V18L10.6 13.4L16.34 19.18L19.18 16.34L13.4 10.6L18 6Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M28.82 31.66L29.66 32.5C30.4 31.38 31.36 30.42 32.5 29.66L31.66 28.82L28.82 31.66Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M16.34 28.82L10.6 34.6L6 30V42H18L13.4 37.4L19.18 31.66L16.34 28.82Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M35.16 44.36C34.26 43.76 33.62 42.98 33.18 42L35.76 40.8C35.98 41.38 36.3 41.82 36.76 42.12C37.22 42.42 37.74 42.56 38.32 42.56C38.9 42.56 39.42 42.46 39.76 42.24C40.1 42.02 40.3 41.74 40.3 41.38C40.3 41.14 40.24 40.94 40.1 40.78C39.98 40.62 39.74 40.46 39.42 40.32C39.08 40.18 38.6 40.02 37.94 39.84C37.08 39.6 36.36 39.34 35.78 39.1C35.2 38.86 34.7 38.46 34.26 37.94C33.82 37.42 33.6 36.72 33.6 35.86C33.6 35.1 33.82 34.4 34.24 33.8C34.68 33.2 35.26 32.72 36 32.38C36.74 32.04 37.56 31.86 38.46 31.86C40.74 31.86 42.38 32.92 43.38 35.02L40.82 36.22C40.54 35.64 40.22 35.22 39.82 34.96C39.42 34.7 38.98 34.58 38.42 34.58C37.9 34.58 37.48 34.68 37.14 34.9C36.8 35.12 36.62 35.38 36.62 35.7C36.62 35.96 36.68 36.16 36.82 36.34C36.96 36.5 37.18 36.66 37.5 36.8C37.82 36.94 38.32 37.1 38.98 37.28C39.82 37.5 40.54 37.74 41.1 37.98C41.68 38.22 42.18 38.6 42.62 39.14C43.06 39.66 43.28 40.36 43.28 41.24C43.28 42.06 43.06 42.76 42.64 43.38C42.22 44 41.62 44.46 40.86 44.8C40.1 45.14 39.22 45.3 38.24 45.3C37.04 45.3 35.98 45 35.1 44.4L35.16 44.36Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
{currentEffect === 44 && <svg width="36" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M40.52 41.5C39.98 41.14 39.58 40.66 39.32 40.06L40.88 39.34C41 39.68 41.2 39.96 41.48 40.14C41.76 40.32 42.06 40.42 42.42 40.42C42.78 40.42 43.08 40.36 43.3 40.22C43.52 40.08 43.62 39.92 43.62 39.7C43.62 39.56 43.58 39.42 43.5 39.34C43.42 39.24 43.28 39.16 43.08 39.06C42.88 38.98 42.58 38.88 42.2 38.76C41.68 38.62 41.24 38.46 40.9 38.32C40.56 38.18 40.26 37.94 39.98 37.62C39.72 37.3 39.58 36.88 39.58 36.36C39.58 35.9 39.72 35.48 39.98 35.12C40.24 34.76 40.6 34.48 41.04 34.26C41.48 34.04 41.98 33.96 42.52 33.96C43.9 33.96 44.88 34.6 45.48 35.86L43.94 36.58C43.78 36.22 43.58 35.98 43.34 35.82C43.1 35.66 42.82 35.58 42.5 35.58C42.2 35.58 41.94 35.64 41.72 35.78C41.52 35.92 41.42 36.08 41.42 36.26C41.42 36.42 41.46 36.54 41.54 36.64C41.62 36.74 41.76 36.84 41.96 36.92C42.16 37 42.46 37.1 42.86 37.22C43.36 37.36 43.8 37.5 44.14 37.64C44.48 37.78 44.8 38.02 45.06 38.34C45.32 38.66 45.46 39.08 45.46 39.6C45.46 40.08 45.34 40.52 45.08 40.9C44.82 41.28 44.46 41.56 44 41.76C43.54 41.96 43.02 42.06 42.42 42.06C41.7 42.06 41.06 41.88 40.52 41.52V41.5Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M31.66 19.18L37.4 13.4L42 18V6H30L34.6 10.6L28.82 16.34L31.66 19.18Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M32.5 29.66L31.66 28.82L28.82 31.66L29.66 32.5C30.4 31.38 31.36 30.42 32.5 29.66Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M18 6H6V18L10.6 13.4L16.34 19.18L19.18 16.34L13.4 10.6L18 6Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M16.34 28.82L10.6 34.6L6 30V42H18L13.4 37.4L19.18 31.66L16.34 28.82Z" fill={currentColor} fillOpacity="0.85"/>
<path d="M34.04 37.76H34L31.46 33.94H29.8V42.04H31.6V37.44H31.66L33.32 39.78H34.72L36.38 37.4H36.44V42.04H38.24V33.94H36.58L34.04 37.76Z" fill={currentColor} fillOpacity="0.85"/>
</svg>
}
    </>
  )
}

export default LightIcon;