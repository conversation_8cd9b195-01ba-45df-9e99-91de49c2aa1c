import { useEffect } from 'react';
import { Slider, ColorPicker, InputNumber } from 'antd';
import { get256HSV, rgbToHex, hsvToRgb } from '../../utils/colorCoversion';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { KeyboardContext } from '../Keyboard/KeyboardContext';
import { useContext, useState } from 'react';
// import { MaterialPicker} from 'react-color';
import { Wheel, Material, Circle } from '@uiw/react-color';
import Saturation from '@uiw/react-color-saturation';
import Hue from '@uiw/react-color-hue';
import LightIcon from './LightIcon';
import { useTranslation } from 'react-i18next';
import './Light.css'


const Light = () => {
  const { t } = useTranslation();
  const { data } = useContext(KeyboardContext);
  const { backlight, setBacklight, addToQueue } = useHandleDevice();
  const [colorHex, setColorHex] = useState('#ffffff');
  const selectedKeys = data.currentSelectedKeycaps;


  const [colorRgb, setColorRgb] = useState({
    r: 255,
    g: 255,
    b: 255,
  });

  const [hsva, setHsva] = useState({ h: 0, s: 0, v: 68, a: 1 });

  const handleBrightnessChange = (value) => {
    setBacklight({ ...backlight, brightness: value });
    const instruction = `07 03 01 ${Number(value).toString(16).padStart(2, '0')} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`;
    addToQueue(instruction);
  }

  const handleSpeedChange = (value) => {
    setBacklight({ ...backlight, speed: value });
    const instruction = `07 03 03 ${Number(value).toString(16).padStart(2, '0')} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`;
    addToQueue(instruction);
  }

  const handleEffectChange = (value) => {
    setBacklight({ ...backlight, mode: value });
    const instruction = `07 03 02 ${Number(value).toString(16).padStart(2, '0')} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`;
    addToQueue(instruction);
  }

  useEffect(() => {
    clearTimeout(window.colorChangeTimer);
    window.colorChangeTimer = setTimeout(() => {
      const rgb = hsvToRgb(hsva.h, hsva.s, hsva.v);
      setColorRgb({ ...colorRgb, r: rgb[0], g: rgb[1], b: rgb[2] });
      const hex = rgbToHex(rgb[0], rgb[1], rgb[2]);
      handleColorChange(hex);
    }, 100);
  }, [hsva]);

  const handleColorChange = (color) => {
    const hsv_arr = get256HSV(color);
    if (backlight.mode === 45) {
      selectedKeys.forEach(key => {
        console.log("hsv_arr", `37 ${key.row} ${key.column} ${Number(hsv_arr[0]).toString(16).padStart(2, '0')} ${Number(hsv_arr[1]).toString(16).padStart(2, '0')} ${Number(hsv_arr[2]).toString(16).padStart(2, '0')}`)
        addToQueue(`37 ${key.row} ${key.column} ${Number(hsv_arr[0]).toString(16).padStart(2, '0')} ${Number(hsv_arr[1]).toString(16).padStart(2, '0')} ${Number(hsv_arr[2]).toString(16).padStart(2, '0')}`);
      });
      addToQueue(`41 01`);
    } else {
      const instruction = `07 03 04 ${Number(hsv_arr[0]).toString(16).padStart(2, '0')} ${Number(hsv_arr[1]).toString(16).padStart(2, '0')} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`;
      addToQueue(instruction);
    }
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
      <div style={{ width: '100%', maxWidth: '1120px', marginTop: '2em' }}>
        <div className="d-flex align-items-center">
          <div style={{ height: '1.5em', width: '0.2em', borderRadius: '0.25em', background: 'var(--bs-primary)', marginRight: '0.5em' }}></div>
          <div style={{ fontSize: '1.2em', fontWeight: 'bold' }}>{t('light.light_preset')}</div>
        </div>
      </div>
      <div className="d-flex">
        <div
          style={{
            marginRight: '1em',
            padding: '1em',
          }}
          className='custom-card-container'
          onTouchMove={(e) => {
            e.currentTarget.style.overflow = 'scroll';
          }}
        >
          {[
            { key: 0, name: t('light.light_mode.0'), icon: '1' },
            { key: 45, name: t('light.light_mode.46'), icon: '2' },
            { key: 1, name: t('light.light_mode.1'), icon: '2' },
            { key: 2, name: t('light.light_mode.2'), icon: '⌨️' },
            { key: 3, name: t('light.light_mode.3'), icon: '↕️' },
            { key: 4, name: t('light.light_mode.4'), icon: '↔️' },
            { key: 5, name: t('light.light_mode.5'), icon: '🌟' },
            { key: 6, name: t('light.light_mode.6'), icon: '🎨' },
            { key: 7, name: t('light.light_mode.7'), icon: '💡' },
            { key: 8, name: t('light.light_mode.8'), icon: '🌈' },
            { key: 9, name: t('light.light_mode.9'), icon: '✨' },
            { key: 10, name: t('light.light_mode.10'), icon: '🌀' },
            { key: 11, name: t('light.light_mode.11'), icon: '💫' },
            { key: 12, name: t('light.light_mode.12'), icon: '🎡' },
            { key: 13, name: t('light.light_mode.13'), icon: '↔️' },
            { key: 14, name: t('light.light_mode.14'), icon: '↕️' },
            { key: 15, name: t('light.light_mode.15'), icon: '🌈' },
            { key: 16, name: t('light.light_mode.16'), icon: '🔄' },
            { key: 17, name: t('light.light_mode.17'), icon: '⭕' },
            { key: 18, name: t('light.light_mode.18'), icon: '🎡' },
            { key: 19, name: t('light.light_mode.19'), icon: '🌀' },
            { key: 20, name: t('light.light_mode.20'), icon: '📡' },
            { key: 21, name: t('light.light_mode.21'), icon: '🌈' },
            { key: 22, name: t('light.light_mode.22'), icon: '🎡' },
            { key: 23, name: t('light.light_mode.23'), icon: '💧' },
            { key: 24, name: t('light.light_mode.24'), icon: '🌈' },
            { key: 25, name: t('light.light_mode.25'), icon: '🌟' },
            { key: 26, name: t('light.light_mode.26'), icon: '🔄' },
            { key: 27, name: t('light.light_mode.27'), icon: '〰️' },
            { key: 28, name: t('light.light_mode.28'), icon: '📱' },
            { key: 29, name: t('light.light_mode.29'), icon: '💫' },
            { key: 30, name: t('light.light_mode.30'), icon: '🔲' },
            { key: 31, name: t('light.light_mode.31'), icon: '🎯' },
            { key: 32, name: t('light.light_mode.32'), icon: '🔢' },
            { key: 33, name: t('light.light_mode.33'), icon: '💫' },
            { key: 34, name: t('light.light_mode.34'), icon: '✨' },
            { key: 35, name: t('light.light_mode.35'), icon: '🌟' },
            { key: 36, name: t('light.light_mode.36'), icon: '🎨' },
            { key: 37, name: t('light.light_mode.37'), icon: '❌' },
            { key: 38, name: t('light.light_mode.38'), icon: '🎯' },
            { key: 39, name: t('light.light_mode.39'), icon: '⚡' },
            { key: 40, name: t('light.light_mode.40'), icon: '🌈' },
            { key: 41, name: t('light.light_mode.41'), icon: '💦' },
            { key: 42, name: t('light.light_mode.42'), icon: '🎨' },
            { key: 43, name: t('light.light_mode.43'), icon: '💫' },
            { key: 44, name: t('light.light_mode.44'), icon: '✨' }
          ].map(effect => (
            <div
              key={effect.key}
              onClick={() => handleEffectChange(effect.key)}
              style={{
                width: '100px',
                height: '100px',
                background: backlight.mode === effect.key ? 'var(--bs-primary)' : '#19191B',
                borderRadius: '0.5em',
                margin: '0.5em',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                transition: 'background 0.2s',
                textAlign: 'center'
              }}
            >
              <div style={{ fontSize: '1.5em', fontWeight: 'bold' }}><LightIcon currentColor="#EFF0F5" currentEffect={effect.key} /></div>
              <div style={{ fontSize: '0.9em', marginTop: '0.5em', color: backlight.mode === effect.key ? '#fff' : '#888' }}>{effect.name}</div>
            </div>
          ))}
        </div>
        <div
          style={{
            width: '700px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          className='custom-card-container'
          onTouchMove={(e) => {
            e.currentTarget.style.overflow = 'scroll';
          }}
        >
          <div>
            <div style={{ width: '220px', height: '220px', borderRadius: '4px', backgroundColor: "#19191B", display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <Saturation
                hsva={hsva}
                onChange={(newColor) => {
                  setHsva({ ...hsva, ...newColor, a: hsva.a });
                }}
              />
            </div>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '220px', height: '40px', backgroundColor: "#19191B", marginTop: "10px" }}>
              <div style={{ width: '200px' }}>
                <Hue
                  hue={hsva.h}
                  onChange={(newHue) => {
                    setHsva({ ...hsva, ...newHue });
                  }}
                />
              </div>
            </div>
            <div style={{ width: '220px', display: 'flex', justifyContent: 'space-between', marginTop: '10px' }}>
              <div style={{ width: '20px', height: '20px', marginTop: '1px', borderRadius: '4px', marginRight: '8px', backgroundColor: colorHex }}></div>
              <div style={{ display: 'flex', flex: "1", alignItems: 'center', gap: '6px', justifyContent: 'space-between', width: '190px' }}>
                {/* <Material
                  className='mater_inputs'
                  color={colorHex}
                  onChange={(color) => {
                  setColorHex(color.hex);
                  // setColorRgb(color.rgb);
                  clearTimeout(window.colorChangeTimer);
                  window.colorChangeTimer = setTimeout(() => {
                    handleColorChange(color);
                  }, 100);
                  }}
                /> */}
                <InputNumber prefix="R" size="small" min={0} max={100} controls={false} value={colorRgb.r} onChange={(value) => {
                  clearTimeout(window.colorChangeTimer);
                  window.colorChangeTimer = setTimeout(() => {
                    // setHsva
                    setColorRgb({ ...colorRgb, r: value });
                    handleColorChange(rgbToHex(value, colorRgb.g, colorRgb.b));
                  }, 100);
                }} />
                <InputNumber prefix="G" size="small" min={0} max={100} controls={false} value={colorRgb.g} onChange={(value) => {
                  clearTimeout(window.colorChangeTimer);
                  window.colorChangeTimer = setTimeout(() => {
                    setColorRgb({ ...colorRgb, g: value });
                    handleColorChange(rgbToHex(colorRgb.r, value, colorRgb.b));
                  }, 100);
                }} />
                <InputNumber prefix="B" size="small" min={0} max={100} controls={false} value={colorRgb.b} onChange={(value) => {
                  clearTimeout(window.colorChangeTimer);
                  window.colorChangeTimer = setTimeout(() => {
                    setColorRgb({ ...colorRgb, b: value });
                    handleColorChange(rgbToHex(colorRgb.r, colorRgb.g, value));
                  }, 100);
                }} />
              </div>
            </div>
          </div>

          <div style={{ marginLeft: '4em' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '2em' }}>
              <div style={{ fontSize: '1.2em', fontWeight: 'bold', marginRight: '1em' }}>{t('light.global_brightness')}</div>
              <div style={{ width: '15em' }}>
                <Slider
                  defaultValue={Math.round((backlight.brightness / 255) * 100)}
                  max={100}
                  min={0}
                  onChange={(value) => handleBrightnessChange(Math.round((value / 100) * 255))}
                  tooltip={{ formatter: (value) => `${value}%` }}
                />
              </div>
            </div>
            {
              (backlight.mode !== 46 && backlight.mode !== 1) && (
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div style={{ fontSize: '1.2em', fontWeight: 'bold', marginRight: '1em' }}>{t('light.global_speed')}</div>
                  <div style={{ width: '15em' }}>
                    <Slider
                      defaultValue={Math.round((backlight.speed / 255) * 100)}
                      max={100}
                      min={0}
                      onChange={(value) => handleSpeedChange(Math.round((value / 100) * 255))}
                      tooltip={{ formatter: (value) => `${value}%` }}
                    />
                  </div>
                </div>
              )
            }
          </div>
        </div>

      </div>
    </div>
  )
}

export default Light;