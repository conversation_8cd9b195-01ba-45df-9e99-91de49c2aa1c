const SetPerformance = (hexArray, setCurrentLayer, updateKeycap, setPerformance, performance) => {
  const layer = 'performance';
  const hand_update = hexArray[0];
  const row = hexArray[1];
  const column = hexArray[2];
  const rt_switch = hexArray[3];

  const rapid_trigger_1 = parseInt(`${hexArray[6]}${hexArray[7]}`, 16);
  // const rapid_trigger_2 = parseInt(`${hexArray[8]}${hexArray[9]}`, 16);
  // const rapid_trigger_3 = parseInt(`${hexArray[10]}${hexArray[11]}`, 16);
  const rapid_trigger_4 = parseInt(`${hexArray[12]}${hexArray[13]}`, 16);
  // 0.01tag
  // let rapid_trigger_4 = parseInt(`${hexArray[12]}${hexArray[13]}`, 16);
  // if (rapid_trigger_4 <= 10) {
  //   rapid_trigger_4 = 0;
  // }
  let rapid_trigger_2 = parseInt(`${hexArray[8]}${hexArray[9]}`, 16);
  let rapid_trigger_3 = parseInt(`${hexArray[10]}${hexArray[11]}`, 16);


  // if (rapid_trigger_2 <= 1) {
  //   rapid_trigger_2 = 0;
  // }

  // if (rapid_trigger_3 <= 1) {
  //   rapid_trigger_3 = 0;
  // }

  const switch_type = hexArray[14];

  let label, mode;
  if (rt_switch === '00') {
    label = (rapid_trigger_1 / 100.00).toFixed(2);
    mode = 'basic';
  } else if (rt_switch === '01') {
    let rapid_trigger_2_label = (rapid_trigger_2 / 100.00).toFixed(2);
    let rapid_trigger_3_label = (rapid_trigger_3 / 100.00).toFixed(2);

    // if (rapid_trigger_3 <= 1) {
    //   rapid_trigger_2_label = '0.01';
    // }
    // if (rapid_trigger_2 <= 1) {
    //   rapid_trigger_3_label = '0.01';
    // }
    label = `${rapid_trigger_2_label}<br/>${rapid_trigger_3_label}`;
    mode = 'rt';
  }

  // 更新按键
  if (hand_update === '19') {
    // setPerformance({
    //   ...performance,
    //   activeKey: mode,
    //   triggerPoint: rapid_trigger_1 / 100.00,
    //   pressTriggerPoint: rapid_trigger_2 / 100.00,
    //   releaseTriggerPoint: rapid_trigger_3 / 100.00,
    //   bottomProtectionPoint: rapid_trigger_4 / 100.00,
    //   switchType: switch_type
    // });
  } else {
    // 全局存储 各种行程值
    updateKeycap(row, column, {
      label,
      rapidTrigger: {
        triggerPoint: rapid_trigger_1 / 100.00,
        pressTravel: rapid_trigger_2 / 100.00,
        releaseStroke: rapid_trigger_3 / 100.00,
        bottomProtectionTravel: rapid_trigger_4 / 100.00,
        switchType: switch_type
      }
    }, layer);
    setPerformance({
      ...performance,
      triggerPoint: rapid_trigger_1 / 100.00,
      pressTriggerPoint: rapid_trigger_2 / 100.00,
      releaseTriggerPoint: rapid_trigger_3 / 100.00,
      bottomProtectionPoint: rapid_trigger_4 / 100.00,
      switchType: switch_type
    });
  }
};

export default SetPerformance;