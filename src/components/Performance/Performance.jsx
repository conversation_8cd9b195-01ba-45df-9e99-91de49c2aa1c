import React, { useState, useEffect } from 'react';
import { Segmented, Slider, message, Button, Switch, Spin, notification, Popover, InputNumber, Modal, Progress} from 'antd';
import { KeyboardContext } from '../Keyboard/KeyboardContext';
import { useHandleDevice } from '../HIDDevice/HandleDeviceContext';
import { useContext } from 'react';
import { changeToHighLowHex } from '../../utils/hidUtils';
import xingguicizhou from '../../assets/images/xingguicizhou.png';
import ciyu_gaming from '../../assets/images/ciyu_gaming.png';
import ciyu_pro from '../../assets/images/ciyu_pro.png';
import ciyu from '../../assets/images/ciyu.png';
import ciyu_max from '../../assets/images/ciyu_max.png';
import shenmi_x_ultra from '../../assets/images/shenmi_x_ultra.png'
import shenmi_x_pro from '../../assets/images/shenmi_x_pro.png'
import { useTranslation } from 'react-i18next';
import StableMode from '../StableMode';

const Performance = () => {
  const { t } = useTranslation();
  const { data, setCurrentSelectedKeycaps, updateKeycap } = useContext(KeyboardContext);
  const { dataQueue, addToQueue, setPerformance, performance, setKeyboardLoading,
    setFullScreenLoading, setFullScreenPercent, setFullScreenLoadingText, stableMode } = useHandleDevice();
  const activeTab = performance.activeKey;
  const selectedKeys = data.currentSelectedKeycaps;
  const [messageApi, contextHolder] = message.useMessage();
  const triggerPoint = performance.triggerPoint;
  const pressTriggerPoint = performance.pressTriggerPoint;
  const releaseTriggerPoint = performance.releaseTriggerPoint;
  const bottomProtectionPoint = performance.bottomProtectionPoint;
  const switchType = performance.switchType;

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [countdown, setCountdown] = useState(15);
  const [progressPercent, setProgressPercent] = useState(100);

  useEffect(() => {
    let timer;
    if (isModalOpen) {
      timer = setInterval(() => {
        setCountdown((prevCountdown) => {
          if (prevCountdown <= 0) {
            clearInterval(timer);
            setIsModalOpen(false);
            return 15;
          }
          return prevCountdown - 0.1;
        });
        setProgressPercent((countdown / 15) * 100);
      }, 100);
    }
    return () => clearInterval(timer);
  }, [isModalOpen, countdown]);

  const content = (
    <div>
      {t('performance.popover_content.quick_trigger_mode_content')}
    </div>
  );

  const contentschedule = (
    <div className='contentschedule' >
      {t('performance.popover_content.release_trigger_point_content')}
    </div>
  );

  const checkSelectedKeys = (callback) => {
    if (selectedKeys.length > 0) {
      callback();
    } else {
      messageApi.info(t('performance.please_select_key'));
    }
  };

  const handleTabChange = (value) => {
    checkSelectedKeys(() => {
      const mode = value === 'basic' ? '00' : '01';
      setPerformance({
        ...performance,
        activeKey: value
      });
    });
  };

  const updatePerformanceValue = (key, value) => {
    // 根据精度自动校准值
    const step = getStep();
    const calibratedValue = Math.round(value / step) * step;
    setPerformance({...performance, [key]: calibratedValue});
    if(calibratedValue === 0 && key === 'bottomProtectionPoint' && stableMode === '01') {
      handleValueChange(calibratedValue)
    }
  };

  const sendPerformanceCommand = (values) => {
    checkSelectedKeys(() => {
      let { triggerPoint, pressTriggerPoint, releaseTriggerPoint, bottomProtectionPoint, switchType } = values;
      const mode = performance.activeKey === 'basic' ? '00' : '01';
      const triggerPointHex = changeToHighLowHex(triggerPoint * 100);
      // 处理切换模式时最小值的bug
      pressTriggerPoint = pressTriggerPoint <= getStep() ? getStep() : pressTriggerPoint;
      releaseTriggerPoint = releaseTriggerPoint <= getStep() ? getStep() : releaseTriggerPoint;
      let pressTriggerPointHex = changeToHighLowHex(pressTriggerPoint * 100);
      let releaseTriggerPointHex = changeToHighLowHex(releaseTriggerPoint * 100);
      const bottomProtectionPointHex = changeToHighLowHex(bottomProtectionPoint * 100);

      if (stableMode === '01') {
        if (pressTriggerPoint <= 0.01) {
          pressTriggerPointHex = changeToHighLowHex(0.01 * 100);
        }

        if (releaseTriggerPoint <= 0.01) {
          releaseTriggerPointHex = changeToHighLowHex(0.01 * 100);
        }
      }

      let label;
      if (mode === '00') {
        label = triggerPoint.toFixed(2);
      } else {
        let pressTriggerPointLabel = pressTriggerPoint.toFixed(2);
        let releaseTriggerPointLabel = releaseTriggerPoint.toFixed(2);
        if (stableMode === '01') {
          if (pressTriggerPoint <= 0.01) {
            pressTriggerPointLabel = '0.01';
          }
          if (releaseTriggerPoint <= 0.01) {
            releaseTriggerPointLabel = '0.01';
          }
        }
        label = `${pressTriggerPointLabel}<br/>${releaseTriggerPointLabel}`;
      }

      selectedKeys.forEach(key => {
        addToQueue(`19 ${key.row} ${key.column} ${mode} 00 00 ${triggerPointHex} ${pressTriggerPointHex} ${releaseTriggerPointHex} ${bottomProtectionPointHex} ${switchType} 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00`);
        updateKeycap(key.row, key.column, {
          label,
          rapidTrigger: {
            triggerPoint: triggerPoint,
            pressTravel: pressTriggerPoint,
            releaseStroke: releaseTriggerPoint,
            bottomProtectionTravel: bottomProtectionPoint,
            switchType: switchType
          }
        }, 'performance');
        setPerformance({
          ...performance,
          triggerPoint: triggerPoint,
          pressTriggerPoint: pressTriggerPoint,
          releaseTriggerPoint: releaseTriggerPoint,
          bottomProtectionPoint: bottomProtectionPoint,
          switchType: switchType
        });
      });
      addToQueue(`41 00`);
      startProgressTracking(20);
    });
  };

  const resetPerformance = () => {
    checkSelectedKeys(() => {
      setCurrentSelectedKeycaps([]);
      setPerformance({...performance, activeKey: ''});
    });
  };

  const startProgressTracking = (totalItems) => {
    setFullScreenLoading(true);
    setFullScreenLoadingText([
      t('performance.loading_text_1'),
      t('performance.loading_text_2')
    ][Math.floor(Math.random() * 2)]);
    setFullScreenPercent(0);

    const totalTimeNeeded = totalItems * 100;
    const startTime = Date.now();

    const interval = setInterval(() => {
      const elapsedTime = Date.now() - startTime;
      const progress = Math.min((elapsedTime / totalTimeNeeded) * 100, 100);

      setFullScreenPercent(progress);

      if (progress >= 100) {
        clearInterval(interval);
        resetPerformance();
        setFullScreenLoading(false);
        setFullScreenPercent(0);
      }
    }, 50);
  };

  const renderSlider = (key, title, value, min, max, onChange, step = 0.01, onComplete) => (
    <div className="mb-2">
      <div className="mb-2">{title}{onComplete}</div>
      <div style={{display: "flex", alignItems: "center"}}>
        <div style={{width: "30em"}}>
          <Slider
            step={step}
            value={value}
            min={min}
            max={max}
            onChange={onChange}
            onChangeComplete={onComplete}
            tooltip={{
              open: false,
            }}
          />
        </div>
        <div>
          <InputNumber
            min={min}
            max={max}
            step={step}
            style={{
              margin: '0 10px',
              width: '75px',
              color: '#ffffff !important'
            }}
            value={(key === 'releaseTriggerPoint' || key === 'pressTriggerPoint') && switchType !== '00' ? (
              (stableMode === '01' ? value <= 0.01 ? '0.01' : value.toFixed(2) : value <= 0.01 ? getStep() : value.toFixed(2))
            ) : (
              value.toFixed(2)
            )}
            onChange={onChange}
          /> <span>mm</span>
        </div>
      </div>
    </div>
  );

  const handleValueChange = (value) => {
    if(value === 0) {
      setIsModalOpen(true);
      setCountdown(15);
      setProgressPercent(100);
    }
    return <></>
  }

  const handleEffectChange = (effect) => {
    let tmpPressTriggerPoint = pressTriggerPoint;
    let tmpReleaseTriggerPoint = releaseTriggerPoint;
    if (effect === '00') {
      if (pressTriggerPoint <= 0.05) {
        tmpPressTriggerPoint = 0.05;
      }
      if (releaseTriggerPoint <= 0.05) {
        tmpReleaseTriggerPoint = 0.05;
      }
      setPerformance({...performance, switchType: effect, pressTriggerPoint: tmpPressTriggerPoint, releaseTriggerPoint: tmpReleaseTriggerPoint});
    } else {
      setPerformance({...performance, switchType: effect});
    }

    // sendPerformanceCommand({triggerPoint, pressTriggerPoint, releaseTriggerPoint, bottomProtectionPoint, switchType: effect});
  };

  const getStep = () => {
    if (stableMode === '02') {
      return 0.1;
    } else if (stableMode === '03') {
      return 0.05;
    } else if (stableMode === '01') {
      return switchType === '00' ? 0.05 : 0.01;
    } else if (stableMode === '00') {
      return switchType === '00' ? 0.05 : 0.01;
    }
  }

  return (
    <div style={{marginTop: "2em"}}>
      {contextHolder}
      <Modal
        centered
        open={isModalOpen}
        closable={false}
        footer={null}
        width={800}
      >
        <div style={{fontSize: '22px', margin: '20px 30px'}}>{t('performance.messages.use_warning')}</div>
        <div style={{display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', margin: '20px 30px'}}>
          <p style={{fontSize: '20px'}}>{t('performance.messages.use_warning_bottom_safe_area')}</p>
        </div>
        <Progress
          percent={progressPercent}
          size="small"
          showInfo={false}
        />
      </Modal>
      <div style={{display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', marginTop: '2em'}}>
        <div className="d-flex">
          <div>
            <div className="d-flex align-items-center" style={{justifyContent: 'space-between'}}>
              <div className="d-flex">
                <div style={{height: '1.5em', width: '0.2em', borderRadius: '0.25em', background: 'var(--bs-primary)', marginRight: '0.5em'}}></div>
                <div style={{fontSize: '1.2em', fontWeight: 'bold'}}>{t('performance.axis_selection')}</div>
              </div>
            </div>
            <div
              style={{
                width: '500px',
                overflow: 'auto',
                borderRadius: '1em',
                marginRight: '1em',
                paddingLeft: '2em',
                paddingTop: '2em',
                paddingBottom: '2em',
                alignContent: 'flex-start',
              }}
              className='custom-card-container'
            >
              {[
                { key: "05", first_name: t('performance.shenmi_x_ultra'), image: shenmi_x_ultra },
                { key: "06", first_name: t('performance.shenmi_x_pro'), image: shenmi_x_pro },
                { key: "00", first_name: t('performance.xingguicizhou'), image: xingguicizhou },
                { key: "01", first_name: t('performance.ciyu_gaming'), image: ciyu_gaming },
                { key: "02", first_name: t('performance.ciyu'), image: ciyu },
                { key: "03", first_name: t('performance.ciyu_pro'), image: ciyu_pro },
                { key: "04", first_name: t('performance.ciyu_max'), image: ciyu_max },
              ].map(effect => (
                <div
                  key={effect.key}
                  onClick={() => handleEffectChange(effect.key)}
                  className={`switch-type-item ${selectedKeys.length > 0 && performance.switchType === effect.key ? 'active' : ''}`}
                >
                  <div style={{fontSize: '1em', fontWeight: 'bold', color: '#888'}}>
                    <div style={{maxWidth: '130px'}}>
                      {effect.first_name}
                    </div>
                  </div>
                  <div style={{marginLeft: '0.5em'}}><img src={effect.image} alt={effect.first_name} style={{width: '70px', height: '62px'}}/></div>
                </div>
              ))}
            </div>
          </div>
          <div>
            <div className="d-flex justify-content-between" style={{marginTop: '-0.8em'}}>
              <div className="d-flex align-items-center">
                <div style={{height: '1.5em', width: '0.2em', borderRadius: '0.25em', background: 'var(--bs-primary)', marginRight: '0.5em'}}></div>
                <div style={{fontSize: '1.2em', fontWeight: 'bold'}}>{t('performance.performance_setting')}</div>
              </div>
              <div style={{height: '34px'}}>
                <StableMode />

                {selectedKeys.length > 0 ? (
                  <>
                    <Button onClick={() => {resetPerformance()}} style={{marginLeft: '1em'}}>
                      {t('cancel')}
                    </Button>
                    <Button type="primary" style={{marginLeft: '1em'}} onClick={() => sendPerformanceCommand({triggerPoint, pressTriggerPoint, releaseTriggerPoint, bottomProtectionPoint, switchType})}>
                      {t('save')}
                    </Button>
                  </>
                ) : (
                  <></>
                )}
              </div>
            </div>

            <div
              style={{
                width: '700px',
                overflow: 'auto',
                display: 'block',
              }}
              className='custom-card-container'
              onTouchMove={(e) => {
                e.currentTarget.style.overflow = 'scroll';
              }}
            >
              <div style={{display: (activeTab === 'basic' || activeTab === 'rt') ? 'block' : 'none'}}>
                <div className='d-flex justify-content-center align-items-center' style={{marginTop: "2em"}}>
                  <div className="choosed-keycap">
                    {renderSlider(
                      'triggerPoint',
                      t('performance.trigger_point'),
                      triggerPoint,
                      0.1,
                      switchType === '00' ? 3.8 : 3.1,
                      value => updatePerformanceValue('triggerPoint', value),
                      switchType === '00' ? 0.05 : 0.01
                    )}
                    <Popover placement="topLeft" content={content}>
                       <div style={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: '2em', width: '34.8em'}}>
                          <div>{t('performance.quick_trigger_mode')}</div>
                         <Switch checked={activeTab === 'rt' ? true : false} onChange={() => handleTabChange(activeTab === 'rt' ? 'basic' : 'rt')} />
                       </div>
                    </Popover>
                    <div style={{fontSize: '0.8em', color: '#888', marginBottom: '2em'}}>{t('performance.quick_trigger_mode_tip')}</div>
                    <Spin indicator={<div></div>} spinning={activeTab === 'basic' ? true : false} size="large">
                       {renderSlider(
                        'pressTriggerPoint',
                        t('performance.press_trigger_point'),
                        pressTriggerPoint,
                        getStep(),
                        switchType === '00' ? 3.8 : 3.1,
                        value => updatePerformanceValue('pressTriggerPoint', value),
                        getStep()
                       )}
                       <Popover placement="topLeft"  content={contentschedule}>
                      {renderSlider(
                        'releaseTriggerPoint',
                        t('performance.release_trigger_point'),
                        releaseTriggerPoint,
                        getStep(),
                        switchType === '00' ? 3.8 : 3.1,
                        value => updatePerformanceValue('releaseTriggerPoint', value),
                        getStep()
                      )}
                       </Popover>
                       {renderSlider(
                        'bottomProtectionPoint',
                        t('performance.bottom_safe_area'),
                        bottomProtectionPoint,
                        0,
                        1,
                        (value) => updatePerformanceValue('bottomProtectionPoint', value),
                        getStep()
                        )}
                    </Spin>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Performance;