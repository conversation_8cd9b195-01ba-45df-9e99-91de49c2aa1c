import KeyCodeMap from "../utils/KeyCodeMap";
import { getLayoutJsonByPid } from "../components/Keyboard/HandleLayout";

// 辅助函数
export const parseHex = (hex) => parseInt(hex.replace(/\s+/g, ''), 16);

export const changeToHex = (decimal_value) =>
  Number(decimal_value).toString(16).padStart(2, '0');

export const changeToHighLowHex = (decimal_value) => {
  const num = Number(decimal_value);
  if (isNaN(num) || num < 0 || num > 0xFFFF) {
    throw new Error("请输入 0 到 65535 (0xFFFF) 范围内的有效数字");
  }

  const high = (num >> 8) & 0xFF;
  const low = num & 0xFF;
  const highHex = high.toString(16).padStart(2, '0').toUpperCase();
  const lowHex = low.toString(16).padStart(2, '0').toUpperCase();

  return `${highHex} ${lowHex}`;
};

export const getPositionByNumber = (start_position, deviceProductId) => {
  let layer, adjustedStartPosition;
  let rowKeyCounts = getLayoutJsonByPid(deviceProductId, true).map(row => row.keys.length);

  const max = Math.max(...rowKeyCounts);
  const filledArray = rowKeyCounts.map(() => max);
  const rows = filledArray;
  const maxRowKeyCount = rows.reduce((sum, value) => sum + value, 0);

  layer = start_position >= maxRowKeyCount ? "01" : "00";
  adjustedStartPosition = start_position >= maxRowKeyCount ? start_position - maxRowKeyCount : start_position;

  if (adjustedStartPosition < 272) {
    var rowIndex, colIndex;
    var row_codes = []

    for (rowIndex = 0; rowIndex < rows.length; rowIndex++) {
      for (colIndex = 0; colIndex < rows[rowIndex]; colIndex++) {
        // 根据行数和列数生成编码
        let rowHex = rowIndex.toString(16).padStart(2, '0').toUpperCase();
        let colHex = colIndex.toString(16).padStart(2, '0').toUpperCase();
        row_codes.push([rowHex, colHex]);
      }
    }

    return { layer: layer, keyPosition: row_codes[adjustedStartPosition] };
  } else {
    return null;
  }
}

export const findNameByCode = (code) => {
  for (let key in KeyCodeMap) {
    if (KeyCodeMap[key].code === code) {
      return KeyCodeMap[key].name;  // 返回对应的 name
    }
  }
  return "default";  // 如果找不到匹配的编码，返回默认值
}

export const findCodeByKey = (key) => {
  return KeyCodeMap[key].code;
}

// 根据按键的 name 查找对应的功能描述
export const findKeyDescriptionByName = (name) => {
  // 直接返回 name，因为 KeyCodeMap 中的 name 字段就是功能描述
  return name || "Unknown";
}

// 根据按键的 label 查找对应的功能描述
export const findKeyDescriptionByLabel = (label) => {
  if (!label) return "Unknown";

  // 处理特殊情况，如包含 <br/> 的标签
  let cleanLabel = label;
  if (label.includes('<br/>')) {
    cleanLabel = label.split('<br/>')[0]; // 取第一部分
  }

  // 移除 HTML 标签
  cleanLabel = cleanLabel.replace(/<[^>]*>/g, '');

  // 在 KeyCodeMap 中查找匹配的 name
  for (let key in KeyCodeMap) {
    if (KeyCodeMap[key].name === cleanLabel) {
      return KeyCodeMap[key].name;
    }
  }

  // 如果没有找到精确匹配，尝试部分匹配
  for (let key in KeyCodeMap) {
    const keyName = KeyCodeMap[key].name;
    // 检查是否是部分匹配（例如 "1 !" 匹配 "1"）
    if (keyName.includes(cleanLabel) || cleanLabel.includes(keyName)) {
      return keyName;
    }
  }

  // 如果仍然没有找到匹配，返回原始 label
  return cleanLabel;
}

export const parseFirmwareVersion = (lengthHex, versionHex) => {
  const length = parseInt(lengthHex, 16); // 将长度从16进制转为整数
  const versionBytes = versionHex.split(" ").slice(0, length); // 根据长度截取版本号字节
  const version = versionBytes
    .map((byte) => String.fromCharCode(parseInt(byte, 16))) // 转换每个字节为字符
    .join(""); // 拼接成字符串
  return version;
};

export const binaryToHex = (binaryString) => {
  const decimal = parseInt(binaryString, 2);
  return decimal.toString(16).toUpperCase().padStart(2, '0');
}

export const hexToBinary = (hexString) => {
  const decimal = parseInt(hexString, 16);
  return decimal.toString(2).padStart(8, '0');
}