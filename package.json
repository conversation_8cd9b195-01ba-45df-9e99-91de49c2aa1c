{"name": "magnetic_drive", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@jaames/iro": "^5.5.2", "@uiw/react-color": "^2.3.4", "@uiw/react-color-hue": "^2.8.0", "@uiw/react-color-saturation": "^2.8.0", "antd": "^5.22.3", "i18next": "^24.2.1", "i18next-browser-languagedetector": "^8.0.2", "react": "^18.3.1", "react-color": "^2.19.3", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-i18next": "^15.4.0", "react-resizable": "^3.0.5", "react-spring": "^9.7.5", "recharts": "^2.15.2", "vite-plugin-i18n-ally": "^5.2.10", "wave-gradient": "^0.1.0"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "fast-glob": "^3.3.3", "globals": "^15.12.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "vite": "^6.0.1", "vite-plugin-obfuscator": "^1.0.5", "vite-plugin-svg-icons": "^2.0.1"}}